package auth

import (
	"context"
	"encoding/json"
	"net/http"
)

// AuthMiddleware provides JWT authentication middleware
type AuthMiddleware struct {
	jwtService *JWTService
	optional   bool
}

// NewAuthMiddleware creates a new auth middleware
func NewAuthMiddleware(jwtService *JWTService, optional bool) *AuthMiddleware {
	return &AuthMiddleware{
		jwtService: jwtService,
		optional:   optional,
	}
}

// Middleware returns the HTTP middleware function
func (m *AuthMiddleware) Middleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// Extract token from Authorization header
		authHeader := r.Header.Get("Authorization")

		if authHeader == "" {
			if m.optional {
				// Continue without authentication
				next.ServeHTTP(w, r)
				return
			}
			m.writeErrorResponse(w, http.StatusUnauthorized, "Authorization header required")
			return
		}

		token, err := ExtractTokenFromHeader(authHeader)
		if err != nil {
			if m.optional {
				// Continue without authentication
				next.ServeHTTP(w, r)
				return
			}
			m.writeErrorResponse(w, http.StatusUnauthorized, "Invalid authorization header")
			return
		}

		// Validate token
		claims, err := m.jwtService.ValidateToken(token)
		if err != nil {
			if m.optional {
				// Continue without authentication
				next.ServeHTTP(w, r)
				return
			}
			m.writeErrorResponse(w, http.StatusUnauthorized, "Invalid token")
			return
		}

		// Add user information to context
		ctx := SetUserInContext(r.Context(), claims.UserID)
		ctx = SetClaimsInContext(ctx, claims)

		// Continue with authenticated request
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

// RequireAuth creates middleware that requires authentication
func (m *AuthMiddleware) RequireAuth(next http.Handler) http.Handler {
	authMiddleware := &AuthMiddleware{
		jwtService: m.jwtService,
		optional:   false,
	}
	return authMiddleware.Middleware(next)
}

// OptionalAuth creates middleware that allows optional authentication
func (m *AuthMiddleware) OptionalAuth(next http.Handler) http.Handler {
	authMiddleware := &AuthMiddleware{
		jwtService: m.jwtService,
		optional:   true,
	}
	return authMiddleware.Middleware(next)
}

// RequireRole creates middleware that requires specific roles
func (m *AuthMiddleware) RequireRole(roles ...UserRole) func(http.Handler) http.Handler {
	return func(next http.Handler) http.Handler {
		return m.RequireAuth(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			claims, ok := GetClaimsFromContext(r.Context())
			if !ok {
				m.writeErrorResponse(w, http.StatusUnauthorized, "Authentication required")
				return
			}

			if !claims.HasAnyRole(roles...) {
				m.writeErrorResponse(w, http.StatusForbidden, "Insufficient permissions")
				return
			}

			next.ServeHTTP(w, r)
		}))
	}
}

// RequirePermission creates middleware that requires specific permissions
func (m *AuthMiddleware) RequirePermission(permission Permission) func(http.Handler) http.Handler {
	return func(next http.Handler) http.Handler {
		return m.RequireAuth(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			claims, ok := GetClaimsFromContext(r.Context())
			if !ok {
				m.writeErrorResponse(w, http.StatusUnauthorized, "Authentication required")
				return
			}

			if !claims.HasPermission(permission) {
				m.writeErrorResponse(w, http.StatusForbidden, "Insufficient permissions")
				return
			}

			next.ServeHTTP(w, r)
		}))
	}
}

// writeErrorResponse writes an error response
func (m *AuthMiddleware) writeErrorResponse(w http.ResponseWriter, statusCode int, message string) {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(statusCode)

	response := map[string]interface{}{
		"error": map[string]interface{}{
			"message": message,
			"code":    statusCode,
		},
	}

	json.NewEncoder(w).Encode(response)
}

// GraphQLAuthMiddleware provides authentication for GraphQL context
type GraphQLAuthMiddleware struct {
	jwtService *JWTService
}

// NewGraphQLAuthMiddleware creates a new GraphQL auth middleware
func NewGraphQLAuthMiddleware(jwtService *JWTService) *GraphQLAuthMiddleware {
	return &GraphQLAuthMiddleware{
		jwtService: jwtService,
	}
}

// ExtractUserFromRequest extracts user information from HTTP request for GraphQL
func (m *GraphQLAuthMiddleware) ExtractUserFromRequest(r *http.Request) context.Context {
	ctx := r.Context()

	// Extract token from Authorization header
	authHeader := r.Header.Get("Authorization")
	if authHeader == "" {
		return ctx
	}

	token, err := ExtractTokenFromHeader(authHeader)
	if err != nil {
		return ctx
	}

	// Validate token
	claims, err := m.jwtService.ValidateToken(token)
	if err != nil {
		return ctx
	}

	// Add user information to context
	ctx = SetUserInContext(ctx, claims.UserID)
	ctx = SetClaimsInContext(ctx, claims)

	return ctx
}

// AuthHandler provides authentication endpoints
type AuthHandler struct {
	jwtService *JWTService
}

// NewAuthHandler creates a new auth handler
func NewAuthHandler(jwtService *JWTService) *AuthHandler {
	return &AuthHandler{
		jwtService: jwtService,
	}
}

// LoginRequest represents login request
type LoginRequest struct {
	Username string `json:"username"`
	Password string `json:"password"`
}

// LoginResponse represents login response
type LoginResponse struct {
	Token     string   `json:"token"`
	ExpiresIn int64    `json:"expires_in"`
	User      MockUser `json:"user"`
}

// Login handles user login
func (h *AuthHandler) Login(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	var req LoginRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		http.Error(w, "Invalid request body", http.StatusBadRequest)
		return
	}

	// Authenticate user
	user, err := AuthenticateUser(req.Username, req.Password)
	if err != nil {
		http.Error(w, "Invalid credentials", http.StatusUnauthorized)
		return
	}

	// Generate JWT token
	token, err := h.jwtService.GenerateToken(user.ID, user.Username, user.Email, user.Roles)
	if err != nil {
		http.Error(w, "Failed to generate token", http.StatusInternalServerError)
		return
	}

	// Return response
	response := LoginResponse{
		Token:     token,
		ExpiresIn: int64(h.jwtService.expiry.Seconds()),
		User:      *user,
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

// Refresh handles token refresh
func (h *AuthHandler) Refresh(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	// Extract token from Authorization header
	authHeader := r.Header.Get("Authorization")
	if authHeader == "" {
		http.Error(w, "Authorization header required", http.StatusUnauthorized)
		return
	}

	token, err := ExtractTokenFromHeader(authHeader)
	if err != nil {
		http.Error(w, "Invalid authorization header", http.StatusUnauthorized)
		return
	}

	// Refresh token
	newToken, err := h.jwtService.RefreshToken(token)
	if err != nil {
		http.Error(w, "Failed to refresh token", http.StatusUnauthorized)
		return
	}

	// Return new token
	response := map[string]interface{}{
		"token":      newToken,
		"expires_in": int64(h.jwtService.expiry.Seconds()),
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

// Me returns current user information
func (h *AuthHandler) Me(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	claims, ok := GetClaimsFromContext(r.Context())
	if !ok {
		http.Error(w, "Authentication required", http.StatusUnauthorized)
		return
	}

	// Return user information
	response := map[string]interface{}{
		"user_id":  claims.UserID,
		"username": claims.Username,
		"email":    claims.Email,
		"roles":    claims.Roles,
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

// CORS middleware
func CORSMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Access-Control-Allow-Origin", "*")
		w.Header().Set("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		w.Header().Set("Access-Control-Allow-Headers", "Content-Type, Authorization")

		if r.Method == "OPTIONS" {
			w.WriteHeader(http.StatusOK)
			return
		}

		next.ServeHTTP(w, r)
	})
}
