package auth

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/golang-jwt/jwt/v5"
)

// UserRole represents user roles
type UserRole string

const (
	RoleAdmin UserRole = "admin"
	RoleUser  UserRole = "user"
	RoleGuest UserRole = "guest"
)

// Claims represents JWT claims
type Claims struct {
	UserID   string   `json:"user_id"`
	Username string   `json:"username"`
	Email    string   `json:"email"`
	Roles    []string `json:"roles"`
	jwt.RegisteredClaims
}

// JWTService handles JWT operations
type JWTService struct {
	secretKey []byte
	expiry    time.Duration
}

// NewJWTService creates a new JWT service
func NewJWTService(secretKey string, expiry time.Duration) *JWTService {
	return &JWTService{
		secretKey: []byte(secretKey),
		expiry:    expiry,
	}
}

// GenerateToken generates a JWT token for a user
func (j *JWTService) GenerateToken(userID, username, email string, roles []string) (string, error) {
	now := time.Now()
	claims := &Claims{
		UserID:   userID,
		Username: username,
		Email:    email,
		Roles:    roles,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(now.Add(j.expiry)),
			IssuedAt:  jwt.NewNumericDate(now),
			NotBefore: jwt.NewNumericDate(now),
			Issuer:    "xbit-cdn-service",
			Subject:   userID,
		},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	return token.SignedString(j.secretKey)
}

// ValidateToken validates a JWT token and returns claims
func (j *JWTService) ValidateToken(tokenString string) (*Claims, error) {
	token, err := jwt.ParseWithClaims(tokenString, &Claims{}, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}
		return j.secretKey, nil
	})

	if err != nil {
		return nil, fmt.Errorf("failed to parse token: %w", err)
	}

	if claims, ok := token.Claims.(*Claims); ok && token.Valid {
		return claims, nil
	}

	return nil, fmt.Errorf("invalid token")
}

// RefreshToken generates a new token with extended expiry
func (j *JWTService) RefreshToken(tokenString string) (string, error) {
	claims, err := j.ValidateToken(tokenString)
	if err != nil {
		return "", fmt.Errorf("invalid token for refresh: %w", err)
	}

	// Generate new token with same claims but extended expiry
	return j.GenerateToken(claims.UserID, claims.Username, claims.Email, claims.Roles)
}

// ExtractTokenFromHeader extracts JWT token from Authorization header
func ExtractTokenFromHeader(authHeader string) (string, error) {
	if authHeader == "" {
		return "", fmt.Errorf("authorization header is empty")
	}

	parts := strings.Split(authHeader, " ")
	if len(parts) != 2 || strings.ToLower(parts[0]) != "bearer" {
		return "", fmt.Errorf("invalid authorization header format")
	}

	return parts[1], nil
}

// HasRole checks if user has a specific role
func (c *Claims) HasRole(role UserRole) bool {
	for _, r := range c.Roles {
		if r == string(role) {
			return true
		}
	}
	return false
}

// HasAnyRole checks if user has any of the specified roles
func (c *Claims) HasAnyRole(roles ...UserRole) bool {
	for _, role := range roles {
		if c.HasRole(role) {
			return true
		}
	}
	return false
}

// IsAdmin checks if user is an admin
func (c *Claims) IsAdmin() bool {
	return c.HasRole(RoleAdmin)
}

// Context keys for storing user information
type contextKey string

const (
	UserContextKey   contextKey = "user"
	ClaimsContextKey contextKey = "claims"
)

// GetUserFromContext extracts user ID from context
func GetUserFromContext(ctx context.Context) (string, bool) {
	userID, ok := ctx.Value(UserContextKey).(string)
	return userID, ok
}

// GetClaimsFromContext extracts claims from context
func GetClaimsFromContext(ctx context.Context) (*Claims, bool) {
	claims, ok := ctx.Value(ClaimsContextKey).(*Claims)
	return claims, ok
}

// SetUserInContext sets user ID in context
func SetUserInContext(ctx context.Context, userID string) context.Context {
	return context.WithValue(ctx, UserContextKey, userID)
}

// SetClaimsInContext sets claims in context
func SetClaimsInContext(ctx context.Context, claims *Claims) context.Context {
	return context.WithValue(ctx, ClaimsContextKey, claims)
}

// Permission represents a permission
type Permission string

const (
	PermissionReadFiles   Permission = "files:read"
	PermissionWriteFiles  Permission = "files:write"
	PermissionDeleteFiles Permission = "files:delete"
	PermissionAdminFiles  Permission = "files:admin"
)

// RolePermissions maps roles to their permissions
var RolePermissions = map[UserRole][]Permission{
	RoleAdmin: {
		PermissionReadFiles,
		PermissionWriteFiles,
		PermissionDeleteFiles,
		PermissionAdminFiles,
	},
	RoleUser: {
		PermissionReadFiles,
		PermissionWriteFiles,
	},
	RoleGuest: {
		PermissionReadFiles,
	},
}

// HasPermission checks if user has a specific permission
func (c *Claims) HasPermission(permission Permission) bool {
	for _, roleStr := range c.Roles {
		role := UserRole(roleStr)
		if permissions, exists := RolePermissions[role]; exists {
			for _, p := range permissions {
				if p == permission {
					return true
				}
			}
		}
	}
	return false
}

// CanReadFiles checks if user can read files
func (c *Claims) CanReadFiles() bool {
	return c.HasPermission(PermissionReadFiles)
}

// CanWriteFiles checks if user can write files
func (c *Claims) CanWriteFiles() bool {
	return c.HasPermission(PermissionWriteFiles)
}

// CanDeleteFiles checks if user can delete files
func (c *Claims) CanDeleteFiles() bool {
	return c.HasPermission(PermissionDeleteFiles)
}

// CanAdminFiles checks if user can perform admin operations on files
func (c *Claims) CanAdminFiles() bool {
	return c.HasPermission(PermissionAdminFiles)
}

// MockUser represents a mock user for testing/demo
type MockUser struct {
	ID       string   `json:"id"`
	Username string   `json:"username"`
	Email    string   `json:"email"`
	Roles    []string `json:"roles"`
}

// MockUsers for demonstration purposes
var MockUsers = map[string]MockUser{
	"admin": {
		ID:       "admin-user-id",
		Username: "admin",
		Email:    "<EMAIL>",
		Roles:    []string{string(RoleAdmin)},
	},
	"user": {
		ID:       "regular-user-id",
		Username: "user",
		Email:    "<EMAIL>",
		Roles:    []string{string(RoleUser)},
	},
	"guest": {
		ID:       "guest-user-id",
		Username: "guest",
		Email:    "<EMAIL>",
		Roles:    []string{string(RoleGuest)},
	},
}

// AuthenticateUser authenticates a user (mock implementation)
func AuthenticateUser(username, password string) (*MockUser, error) {
	// This is a mock implementation for demonstration
	// In a real application, you would validate against a database
	if user, exists := MockUsers[username]; exists {
		// In a real implementation, you would hash and compare passwords
		if password == "password" || (username == "admin" && password == "admin123") {
			return &user, nil
		}
	}
	return nil, fmt.Errorf("invalid credentials")
}
