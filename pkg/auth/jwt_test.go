package auth

import (
	"testing"
	"time"
)

func TestJWTService_GenerateAndValidateToken(t *testing.T) {
	jwtService := NewJWTService("test-secret-key", time.Hour)

	userID := "test-user-id"
	username := "testuser"
	email := "<EMAIL>"
	roles := []string{"user", "admin"}

	// Generate token
	token, err := jwtService.GenerateToken(userID, username, email, roles)
	if err != nil {
		t.Fatalf("Failed to generate token: %v", err)
	}

	if token == "" {
		t.Fatal("Generated token is empty")
	}

	// Validate token
	claims, err := jwtService.ValidateToken(token)
	if err != nil {
		t.Fatalf("Failed to validate token: %v", err)
	}

	// Check claims
	if claims.UserID != userID {
		t.Errorf("Expected UserID %s, got %s", userID, claims.UserID)
	}

	if claims.Username != username {
		t.<PERSON><PERSON>("Expected Username %s, got %s", username, claims.Username)
	}

	if claims.Email != email {
		t.Errorf("Expected Email %s, got %s", email, claims.Email)
	}

	if len(claims.Roles) != len(roles) {
		t.Errorf("Expected %d roles, got %d", len(roles), len(claims.Roles))
	}

	for i, role := range roles {
		if claims.Roles[i] != role {
			t.Errorf("Expected role %s at index %d, got %s", role, i, claims.Roles[i])
		}
	}
}

func TestJWTService_ValidateInvalidToken(t *testing.T) {
	jwtService := NewJWTService("test-secret-key", time.Hour)

	// Test with invalid token
	_, err := jwtService.ValidateToken("invalid-token")
	if err == nil {
		t.Fatal("Expected error for invalid token, got nil")
	}

	// Test with empty token
	_, err = jwtService.ValidateToken("")
	if err == nil {
		t.Fatal("Expected error for empty token, got nil")
	}
}

func TestJWTService_RefreshToken(t *testing.T) {
	jwtService := NewJWTService("test-secret-key", time.Hour)

	userID := "test-user-id"
	username := "testuser"
	email := "<EMAIL>"
	roles := []string{"user"}

	// Generate original token
	originalToken, err := jwtService.GenerateToken(userID, username, email, roles)
	if err != nil {
		t.Fatalf("Failed to generate original token: %v", err)
	}

	// Refresh token
	newToken, err := jwtService.RefreshToken(originalToken)
	if err != nil {
		t.Fatalf("Failed to refresh token: %v", err)
	}

	if newToken == "" {
		t.Fatal("Refreshed token is empty")
	}

	// Note: Tokens might be the same if generated within the same second
	// This is expected behavior since JWT includes timestamp in seconds
	// The important thing is that both tokens are valid

	// Validate new token
	claims, err := jwtService.ValidateToken(newToken)
	if err != nil {
		t.Fatalf("Failed to validate refreshed token: %v", err)
	}

	if claims.UserID != userID {
		t.Errorf("Expected UserID %s in refreshed token, got %s", userID, claims.UserID)
	}
}

func TestExtractTokenFromHeader(t *testing.T) {
	tests := []struct {
		name          string
		authHeader    string
		expectedErr   bool
		expectedToken string
	}{
		{
			name:          "valid bearer token",
			authHeader:    "Bearer abc123",
			expectedErr:   false,
			expectedToken: "abc123",
		},
		{
			name:        "empty header",
			authHeader:  "",
			expectedErr: true,
		},
		{
			name:        "invalid format - no bearer",
			authHeader:  "abc123",
			expectedErr: true,
		},
		{
			name:        "invalid format - wrong prefix",
			authHeader:  "Basic abc123",
			expectedErr: true,
		},
		{
			name:        "invalid format - no token",
			authHeader:  "Bearer",
			expectedErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			token, err := ExtractTokenFromHeader(tt.authHeader)

			if tt.expectedErr {
				if err == nil {
					t.Errorf("Expected error for header '%s', got nil", tt.authHeader)
				}
			} else {
				if err != nil {
					t.Errorf("Unexpected error for header '%s': %v", tt.authHeader, err)
				}
				if token != tt.expectedToken {
					t.Errorf("Expected token '%s', got '%s'", tt.expectedToken, token)
				}
			}
		})
	}
}

func TestClaims_HasRole(t *testing.T) {
	claims := &Claims{
		Roles: []string{"user", "admin"},
	}

	tests := []struct {
		role     UserRole
		expected bool
	}{
		{RoleUser, true},
		{RoleAdmin, true},
		{RoleGuest, false},
	}

	for _, tt := range tests {
		t.Run(string(tt.role), func(t *testing.T) {
			result := claims.HasRole(tt.role)
			if result != tt.expected {
				t.Errorf("HasRole(%s) = %v, expected %v", tt.role, result, tt.expected)
			}
		})
	}
}

func TestClaims_HasAnyRole(t *testing.T) {
	claims := &Claims{
		Roles: []string{"user"},
	}

	// Should return true if user has any of the specified roles
	if !claims.HasAnyRole(RoleUser, RoleAdmin) {
		t.Error("Expected HasAnyRole(user, admin) to return true")
	}

	// Should return false if user has none of the specified roles
	if claims.HasAnyRole(RoleAdmin, RoleGuest) {
		t.Error("Expected HasAnyRole(admin, guest) to return false")
	}
}

func TestClaims_HasPermission(t *testing.T) {
	tests := []struct {
		name       string
		roles      []string
		permission Permission
		expected   bool
	}{
		{
			name:       "admin has all permissions",
			roles:      []string{"admin"},
			permission: PermissionAdminFiles,
			expected:   true,
		},
		{
			name:       "user can read files",
			roles:      []string{"user"},
			permission: PermissionReadFiles,
			expected:   true,
		},
		{
			name:       "user can write files",
			roles:      []string{"user"},
			permission: PermissionWriteFiles,
			expected:   true,
		},
		{
			name:       "user cannot delete files",
			roles:      []string{"user"},
			permission: PermissionDeleteFiles,
			expected:   false,
		},
		{
			name:       "guest can only read files",
			roles:      []string{"guest"},
			permission: PermissionReadFiles,
			expected:   true,
		},
		{
			name:       "guest cannot write files",
			roles:      []string{"guest"},
			permission: PermissionWriteFiles,
			expected:   false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			claims := &Claims{Roles: tt.roles}
			result := claims.HasPermission(tt.permission)
			if result != tt.expected {
				t.Errorf("HasPermission(%s) = %v, expected %v", tt.permission, result, tt.expected)
			}
		})
	}
}

func TestAuthenticateUser(t *testing.T) {
	tests := []struct {
		name        string
		username    string
		password    string
		expectError bool
	}{
		{
			name:        "valid admin credentials",
			username:    "admin",
			password:    "admin123",
			expectError: false,
		},
		{
			name:        "valid user credentials",
			username:    "user",
			password:    "password",
			expectError: false,
		},
		{
			name:        "invalid username",
			username:    "nonexistent",
			password:    "password",
			expectError: true,
		},
		{
			name:        "invalid password",
			username:    "user",
			password:    "wrongpassword",
			expectError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			user, err := AuthenticateUser(tt.username, tt.password)

			if tt.expectError {
				if err == nil {
					t.Errorf("Expected error for credentials %s:%s, got nil", tt.username, tt.password)
				}
				if user != nil {
					t.Errorf("Expected nil user for invalid credentials, got %+v", user)
				}
			} else {
				if err != nil {
					t.Errorf("Unexpected error for credentials %s:%s: %v", tt.username, tt.password, err)
				}
				if user == nil {
					t.Error("Expected user object, got nil")
				} else {
					if user.Username != tt.username {
						t.Errorf("Expected username %s, got %s", tt.username, user.Username)
					}
				}
			}
		})
	}
}
