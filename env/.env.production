# Production Environment Variables
ENV=production

# Server Configuration
PORT=8080
HOST=0.0.0.0
DEBUG=false
LOG_LEVEL=warn

# Database Configuration
DB_HOST=CHANGE_ME_PRODUCTION_DB_HOST
DB_PORT=5432
DB_USER=CHANGE_ME_PRODUCTION_DB_USER
DB_PASSWORD=CHANGE_ME_PRODUCTION_DB_PASSWORD
DB_NAME=CHANGE_ME_PRODUCTION_DB_NAME
DB_SSLMODE=require
DB_MAX_CONNECTIONS=100
DB_MAX_IDLE_CONNECTIONS=50
DB_CONNECTION_TIMEOUT=30s
DB_IDLE_TIMEOUT=10m

# Cloudflare R2 Configuration
R2_ACCOUNT_ID=CHANGE_ME_R2_ACCOUNT_ID
R2_ACCESS_KEY_ID=CHANGE_ME_R2_ACCESS_KEY_ID
R2_SECRET_ACCESS_KEY=CHANGE_ME_R2_SECRET_ACCESS_KEY
R2_BUCKET_NAME=CHANGE_ME_R2_BUCKET_NAME
R2_ENDPOINT=https://CHANGE_ME_ACCOUNT_ID.r2.cloudflarestorage.com

# Cloudflare CDN Configuration
CDN_BASE_URL=CHANGE_ME_CDN_BASE_URL
CDN_ZONE_ID=CHANGE_ME_CDN_ZONE_ID
CDN_API_TOKEN=CHANGE_ME_CDN_API_TOKEN

# JWT Configuration
JWT_SECRET=CHANGE_ME_PRODUCTION_JWT_SECRET_KEY
JWT_EXPIRY=12h
JWT_REFRESH_EXPIRY=168h

# File Upload Configuration
MAX_FILE_SIZE=2GB
ALLOWED_EXTENSIONS=jpg,jpeg,png,gif,mp4,mov,avi,webm,pdf,doc,docx,zip,rar,7z
SIGNED_URL_EXPIRY=6h
TEMP_DIR=/tmp/xbit-uploads

# Redis Configuration
REDIS_HOST=CHANGE_ME_REDIS_HOST
REDIS_PORT=6379
REDIS_PASSWORD=CHANGE_ME_REDIS_PASSWORD
REDIS_DB=0
REDIS_ENABLED=true
REDIS_CLUSTER_MODE=true

# CORS Configuration
CORS_ALLOWED_ORIGINS=CHANGE_ME_FRONTEND_URL,CHANGE_ME_ADMIN_URL
CORS_ALLOWED_METHODS=GET,POST,PUT,DELETE,OPTIONS
CORS_ALLOWED_HEADERS=Authorization,Content-Type,X-Requested-With
CORS_ALLOW_CREDENTIALS=true

# Monitoring Configuration
METRICS_ENABLED=true
HEALTH_CHECK_ENABLED=true
PROFILING_ENABLED=false

# Security Configuration
ENABLE_HTTPS=true
HSTS_ENABLED=true
CSRF_PROTECTION=true
API_KEY_REQUIRED=true

# Rate Limiting
RATE_LIMITING_ENABLED=true
REQUESTS_PER_MINUTE=200
BURST_SIZE=20

# Frontend URLs
FRONTEND_URL=CHANGE_ME_FRONTEND_URL
ADMIN_URL=CHANGE_ME_ADMIN_URL

# Backup Configuration
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 1 * * *
BACKUP_RETENTION_DAYS=90
S3_BACKUP_ENABLED=true

# Logging Configuration
LOG_STRUCTURED=true
LOG_FORMAT=json
LOG_OUTPUT=file
LOG_FILE_PATH=/var/log/xbit-cdn/app.log
LOG_MAX_SIZE=100MB
LOG_MAX_BACKUPS=10
LOG_MAX_AGE=30
