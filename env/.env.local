# Local Development Environment Variables
ENV=local

# Server Configuration
PORT=8080
HOST=localhost
DEBUG=true
LOG_LEVEL=debug

# Database Configuration
DB_HOST=localhost
DB_PORT=5433
DB_USER=postgres
DB_PASSWORD=postgres
DB_NAME=xbit_cdn_local
DB_SSLMODE=disable
DB_MAX_CONNECTIONS=10
DB_MAX_IDLE_CONNECTIONS=5

# Cloudflare R2 Configuration (Local - use test credentials or leave empty)
R2_ACCOUNT_ID=23730d7e6dec9dfe0266e9df554e20c5
R2_ACCESS_KEY_ID=659727ae7f867e3a8430d26d8200b6dc
R2_SECRET_ACCESS_KEY=3a91eb858bd3335ba0c7855fec0322794171a38d1aaec5794f931b0c489faa50
R2_BUCKET_NAME=xbit-unstable
R2_ENDPOINT=https://23730d7e6dec9dfe0266e9df554e20c5.r2.cloudflarestorage.com

# Cloudflare CDN Configuration (Local)
CDN_BASE_URL=http://localhost:8080
CDN_ZONE_ID=your_test_zone_id
CDN_API_TOKEN=your_test_api_token

# JWT Configuration
JWT_SECRET=local-development-secret-key-change-in-production
JWT_EXPIRY=24h
JWT_REFRESH_EXPIRY=168h

# File Upload Configuration
MAX_FILE_SIZE=100MB
ALLOWED_EXTENSIONS=jpg,jpeg,png,gif,mp4,mov,avi,webm,pdf,doc,docx
SIGNED_URL_EXPIRY=1h
TEMP_DIR=/tmp/xbit-uploads

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0
REDIS_ENABLED=true

# CORS Configuration
CORS_ALLOWED_ORIGINS=http://localhost:3000,http://localhost:8080
CORS_ALLOWED_METHODS=GET,POST,PUT,DELETE,OPTIONS
CORS_ALLOWED_HEADERS=*
CORS_ALLOW_CREDENTIALS=true

# Monitoring Configuration
METRICS_ENABLED=true
HEALTH_CHECK_ENABLED=true
PROFILING_ENABLED=true

# Security Configuration
ENABLE_HTTPS=false
HSTS_ENABLED=false
CSRF_PROTECTION=false

# Rate Limiting
RATE_LIMITING_ENABLED=false
REQUESTS_PER_MINUTE=1000
BURST_SIZE=100
