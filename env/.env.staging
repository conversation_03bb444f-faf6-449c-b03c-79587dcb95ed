# Staging Environment Variables
ENV=staging

# Server Configuration
PORT=8080
HOST=0.0.0.0
DEBUG=false
LOG_LEVEL=info

# Database Configuration
DB_HOST=staging-db.internal
DB_PORT=5432
DB_USER=xbit_user
DB_PASSWORD=CHANGE_ME_STAGING_DB_PASSWORD
DB_NAME=xbit_cdn_staging
DB_SSLMODE=require
DB_MAX_CONNECTIONS=50
DB_MAX_IDLE_CONNECTIONS=25

# Cloudflare R2 Configuration
R2_ACCOUNT_ID=CHANGE_ME_R2_ACCOUNT_ID
R2_ACCESS_KEY_ID=CHANGE_ME_R2_ACCESS_KEY_ID
R2_SECRET_ACCESS_KEY=CHANGE_ME_R2_SECRET_ACCESS_KEY
R2_BUCKET_NAME=xbit-cdn-staging
R2_ENDPOINT=https://CHANGE_ME_ACCOUNT_ID.r2.cloudflarestorage.com

# Cloudflare CDN Configuration
CDN_BASE_URL=https://staging-cdn.xbit.com
CDN_ZONE_ID=CHANGE_ME_CDN_ZONE_ID
CDN_API_TOKEN=CHANGE_ME_CDN_API_TOKEN

# JWT Configuration
JWT_SECRET=CHANGE_ME_STAGING_JWT_SECRET_KEY
JWT_EXPIRY=24h
JWT_REFRESH_EXPIRY=168h

# File Upload Configuration
MAX_FILE_SIZE=1GB
ALLOWED_EXTENSIONS=jpg,jpeg,png,gif,mp4,mov,avi,webm,pdf,doc,docx,zip,rar,7z
SIGNED_URL_EXPIRY=4h
TEMP_DIR=/tmp/xbit-uploads

# Redis Configuration
REDIS_HOST=staging-redis.internal
REDIS_PORT=6379
REDIS_PASSWORD=CHANGE_ME_REDIS_PASSWORD
REDIS_DB=0
REDIS_ENABLED=true

# CORS Configuration
CORS_ALLOWED_ORIGINS=https://staging.xbit.com,https://admin-staging.xbit.com
CORS_ALLOWED_METHODS=GET,POST,PUT,DELETE,OPTIONS
CORS_ALLOWED_HEADERS=*
CORS_ALLOW_CREDENTIALS=true

# Monitoring Configuration
METRICS_ENABLED=true
HEALTH_CHECK_ENABLED=true
PROFILING_ENABLED=false

# Security Configuration
ENABLE_HTTPS=true
HSTS_ENABLED=true
CSRF_PROTECTION=true

# Rate Limiting
RATE_LIMITING_ENABLED=true
REQUESTS_PER_MINUTE=500
BURST_SIZE=50

# Frontend URLs
FRONTEND_URL=https://staging.xbit.com
ADMIN_URL=https://admin-staging.xbit.com

# Backup Configuration
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30

# Logging Configuration
LOG_FORMAT=json
LOG_OUTPUT=file
LOG_FILE_PATH=/var/log/xbit-cdn/app.log
LOG_MAX_SIZE=100MB
LOG_MAX_BACKUPS=10
LOG_MAX_AGE=30
