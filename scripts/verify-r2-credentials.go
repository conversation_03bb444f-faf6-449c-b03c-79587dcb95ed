package main

import (
	"context"
	"fmt"
	"log"
	"os"
	"strings"
	"time"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/credentials"
	"github.com/aws/aws-sdk-go-v2/service/s3"
)

func main() {
	fmt.Println("🔐 Verifying Cloudflare R2 Credentials...")
	fmt.Println("=========================================")

	// Load environment variables from .env.unstable
	if err := loadEnvFile("env/.env.unstable"); err != nil {
		log.Fatalf("❌ Failed to load environment file: %v", err)
	}

	// Get R2 configuration from environment
	accountID := os.Getenv("R2_ACCOUNT_ID")
	accessKeyID := os.Getenv("R2_ACCESS_KEY_ID")
	secretAccessKey := os.Getenv("R2_SECRET_ACCESS_KEY")
	bucketName := os.Getenv("R2_BUCKET_NAME")
	endpoint := os.Getenv("R2_ENDPOINT")

	// Validate configuration
	if accountID == "" || accessKeyID == "" || secretAccessKey == "" || bucketName == "" || endpoint == "" {
		log.Fatal("❌ Missing R2 configuration. Please check your .env.unstable file.")
	}

	fmt.Printf("📋 Configuration Summary:\n")
	fmt.Printf("   Account ID: %s\n", accountID)
	fmt.Printf("   Access Key ID: %s***\n", accessKeyID[:8])
	fmt.Printf("   Secret Key: %s***\n", secretAccessKey[:8])
	fmt.Printf("   Bucket Name: %s\n", bucketName)
	fmt.Printf("   Endpoint: %s\n", endpoint)
	fmt.Println()

	// Validate endpoint format
	expectedEndpoint := fmt.Sprintf("https://%s.r2.cloudflarestorage.com", accountID)
	if endpoint != expectedEndpoint {
		fmt.Printf("⚠️  Warning: Endpoint mismatch!\n")
		fmt.Printf("   Expected: %s\n", expectedEndpoint)
		fmt.Printf("   Actual:   %s\n", endpoint)
		fmt.Println("   This might cause connection issues.")
		fmt.Println()
	} else {
		fmt.Println("✅ Endpoint format is correct!")
	}

	// Create custom resolver for R2 endpoint
	customResolver := aws.EndpointResolverWithOptionsFunc(func(service, region string, options ...interface{}) (aws.Endpoint, error) {
		if service == s3.ServiceID {
			return aws.Endpoint{
				URL:           endpoint,
				SigningRegion: "auto",
			}, nil
		}
		return aws.Endpoint{}, fmt.Errorf("unknown endpoint requested")
	})

	// Load AWS config with custom credentials and endpoint
	awsCfg, err := config.LoadDefaultConfig(context.TODO(),
		config.WithCredentialsProvider(credentials.NewStaticCredentialsProvider(
			accessKeyID,
			secretAccessKey,
			"",
		)),
		config.WithEndpointResolverWithOptions(customResolver),
		config.WithRegion("auto"),
	)
	if err != nil {
		log.Fatalf("❌ Failed to load AWS config: %v", err)
	}

	// Create S3 client
	client := s3.NewFromConfig(awsCfg)

	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	fmt.Println("🔍 Testing credential validity...")

	// Test 1: Try to list buckets (this might fail due to permissions, but will validate credentials)
	_, err = client.ListBuckets(ctx, &s3.ListBucketsInput{})
	if err != nil {
		if strings.Contains(err.Error(), "AccessDenied") {
			fmt.Println("✅ Credentials are valid (Access Denied is expected for ListBuckets)")
		} else if strings.Contains(err.Error(), "InvalidAccessKeyId") {
			fmt.Println("❌ Invalid Access Key ID")
			return
		} else if strings.Contains(err.Error(), "SignatureDoesNotMatch") {
			fmt.Println("❌ Invalid Secret Access Key")
			return
		} else {
			fmt.Printf("⚠️  Unexpected error: %v\n", err)
		}
	} else {
		fmt.Println("✅ Credentials are valid and have ListBuckets permission!")
	}

	fmt.Printf("\n🪣 Testing bucket '%s' access...\n", bucketName)

	// Test 2: Try to access the specific bucket
	_, err = client.HeadBucket(ctx, &s3.HeadBucketInput{
		Bucket: aws.String(bucketName),
	})

	if err != nil {
		if strings.Contains(err.Error(), "NotFound") || strings.Contains(err.Error(), "NoSuchBucket") {
			fmt.Printf("❌ Bucket '%s' does not exist\n", bucketName)
			fmt.Println("   Action required: Create the bucket in Cloudflare Dashboard")
		} else if strings.Contains(err.Error(), "Forbidden") || strings.Contains(err.Error(), "AccessDenied") {
			fmt.Printf("⚠️  Bucket '%s' exists but access is denied\n", bucketName)
			fmt.Println("   Possible reasons:")
			fmt.Println("   1. Bucket exists but credentials don't have access")
			fmt.Println("   2. Bucket is in a different account")
			fmt.Println("   3. API token needs Object Storage permissions")
		} else {
			fmt.Printf("❌ Unexpected error accessing bucket: %v\n", err)
		}
	} else {
		fmt.Printf("✅ Bucket '%s' is accessible!\n", bucketName)
	}

	fmt.Println("\n📊 Configuration Status Summary:")
	fmt.Println("================================")
	
	// Check each component
	fmt.Printf("Account ID:        %s\n", getStatus(accountID != ""))
	fmt.Printf("Access Key ID:     %s\n", getStatus(accessKeyID != ""))
	fmt.Printf("Secret Access Key: %s\n", getStatus(secretAccessKey != ""))
	fmt.Printf("Bucket Name:       %s\n", getStatus(bucketName != ""))
	fmt.Printf("Endpoint Format:   %s\n", getStatus(endpoint == expectedEndpoint))

	fmt.Println("\n📝 Next Steps:")
	fmt.Println("==============")
	
	if endpoint != expectedEndpoint {
		fmt.Println("1. ⚠️  Fix endpoint URL in .env.unstable")
	}
	
	fmt.Printf("2. 🪣 Ensure bucket '%s' exists in Cloudflare Dashboard\n", bucketName)
	fmt.Println("3. 🔑 Verify API token has 'Object Storage:Edit' permissions")
	fmt.Println("4. 🧪 Run 'make test-r2-unstable' after fixing issues")
	fmt.Println("5. 📖 Check docs/R2_SETUP_GUIDE.md for detailed instructions")

	fmt.Println("\n💡 Tips:")
	fmt.Println("========")
	fmt.Println("- The bucket must be created manually in Cloudflare Dashboard")
	fmt.Println("- API tokens need 'Object Storage:Edit' permission minimum")
	fmt.Println("- Access Denied for ListBuckets is normal and expected")
	fmt.Println("- Contact DevOps if you need help with bucket creation")
}

func getStatus(condition bool) string {
	if condition {
		return "✅ OK"
	}
	return "❌ Issue"
}

// Simple function to load environment variables from a file
func loadEnvFile(filename string) error {
	file, err := os.Open(filename)
	if err != nil {
		return err
	}
	defer file.Close()

	content := make([]byte, 0, 1024)
	buffer := make([]byte, 1024)
	for {
		n, err := file.Read(buffer)
		if n > 0 {
			content = append(content, buffer[:n]...)
		}
		if err != nil {
			break
		}
	}

	lines := strings.Split(string(content), "\n")
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" || strings.HasPrefix(line, "#") {
			continue
		}
		
		parts := strings.SplitN(line, "=", 2)
		if len(parts) == 2 {
			key := strings.TrimSpace(parts[0])
			value := strings.TrimSpace(parts[1])
			os.Setenv(key, value)
		}
	}

	return nil
}
