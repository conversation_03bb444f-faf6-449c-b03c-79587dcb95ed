package main

import (
	"context"
	"fmt"
	"log"
	"os"
	"strings"
	"time"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/credentials"
	"github.com/aws/aws-sdk-go-v2/service/s3"
	"github.com/aws/aws-sdk-go-v2/service/s3/types"
)

func main() {
	fmt.Println("🚀 Setting up Cloudflare R2 Bucket...")
	fmt.Println("=====================================")

	// Load environment variables from .env.unstable
	if err := loadEnvFile("env/.env.unstable"); err != nil {
		log.Fatalf("❌ Failed to load environment file: %v", err)
	}

	// Get R2 configuration from environment
	accountID := os.Getenv("R2_ACCOUNT_ID")
	accessKeyID := os.Getenv("R2_ACCESS_KEY_ID")
	secretAccessKey := os.Getenv("R2_SECRET_ACCESS_KEY")
	bucketName := os.Getenv("R2_BUCKET_NAME")
	endpoint := os.Getenv("R2_ENDPOINT")

	// Validate configuration
	if accountID == "" || accessKeyID == "" || secretAccessKey == "" || bucketName == "" || endpoint == "" {
		log.Fatal("❌ Missing R2 configuration. Please check your .env.unstable file.")
	}

	fmt.Printf("📋 Configuration:\n")
	fmt.Printf("   Account ID: %s\n", accountID)
	fmt.Printf("   Access Key ID: %s\n", accessKeyID[:8]+"...")
	fmt.Printf("   Bucket Name: %s\n", bucketName)
	fmt.Printf("   Endpoint: %s\n", endpoint)
	fmt.Println()

	// Create custom resolver for R2 endpoint
	customResolver := aws.EndpointResolverWithOptionsFunc(func(service, region string, options ...interface{}) (aws.Endpoint, error) {
		if service == s3.ServiceID {
			return aws.Endpoint{
				URL:           endpoint,
				SigningRegion: "auto",
			}, nil
		}
		return aws.Endpoint{}, fmt.Errorf("unknown endpoint requested")
	})

	// Load AWS config with custom credentials and endpoint
	awsCfg, err := config.LoadDefaultConfig(context.TODO(),
		config.WithCredentialsProvider(credentials.NewStaticCredentialsProvider(
			accessKeyID,
			secretAccessKey,
			"",
		)),
		config.WithEndpointResolverWithOptions(customResolver),
		config.WithRegion("auto"),
	)
	if err != nil {
		log.Fatalf("❌ Failed to load AWS config: %v", err)
	}

	// Create S3 client
	client := s3.NewFromConfig(awsCfg)

	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// Check if bucket exists
	fmt.Printf("🔍 Checking if bucket '%s' exists...\n", bucketName)
	_, err = client.HeadBucket(ctx, &s3.HeadBucketInput{
		Bucket: aws.String(bucketName),
	})

	if err != nil {
		fmt.Printf("⚠️  Bucket doesn't exist or is not accessible: %v\n", err)
		fmt.Printf("🛠️  Attempting to create bucket '%s'...\n", bucketName)

		// Try to create the bucket
		_, err = client.CreateBucket(ctx, &s3.CreateBucketInput{
			Bucket: aws.String(bucketName),
		})

		if err != nil {
			log.Printf("❌ Failed to create bucket: %v", err)
			log.Println("   Possible reasons:")
			log.Println("   1. Bucket name already exists globally")
			log.Println("   2. Insufficient permissions to create buckets")
			log.Println("   3. Invalid bucket name")
			log.Println("   Please create the bucket manually in Cloudflare Dashboard")
			return
		}

		fmt.Printf("✅ Successfully created bucket '%s'!\n", bucketName)

		// Wait a moment for bucket to be ready
		fmt.Println("⏳ Waiting for bucket to be ready...")
		time.Sleep(3 * time.Second)
	} else {
		fmt.Printf("✅ Bucket '%s' already exists and is accessible!\n", bucketName)
	}

	// Set up CORS configuration for web uploads
	fmt.Println("🌐 Setting up CORS configuration...")
	corsConfig := &s3.PutBucketCorsInput{
		Bucket: aws.String(bucketName),
		CORSConfiguration: &types.CORSConfiguration{
			CORSRules: []types.CORSRule{
				{
					AllowedHeaders: []string{"*"},
					AllowedMethods: []string{"GET", "PUT", "POST", "DELETE", "HEAD"},
					AllowedOrigins: []string{
						"https://unstable.xbit.com",
						"https://admin-unstable.xbit.com",
						"http://localhost:3000", // For local development
						"http://localhost:8080", // For local API
					},
					ExposeHeaders: []string{"ETag"},
					MaxAgeSeconds: aws.Int32(3600),
				},
			},
		},
	}

	_, err = client.PutBucketCors(ctx, corsConfig)
	if err != nil {
		log.Printf("⚠️  Failed to set CORS configuration: %v", err)
		log.Println("   You may need to set CORS manually in Cloudflare Dashboard")
	} else {
		fmt.Println("✅ CORS configuration set successfully!")
	}

	// Test basic operations
	fmt.Println("🧪 Testing basic bucket operations...")
	
	// Test upload
	testKey := fmt.Sprintf("setup-test-%d.txt", time.Now().Unix())
	testContent := "This is a test file created during R2 bucket setup"
	
	_, err = client.PutObject(ctx, &s3.PutObjectInput{
		Bucket:      aws.String(bucketName),
		Key:         aws.String(testKey),
		Body:        strings.NewReader(testContent),
		ContentType: aws.String("text/plain"),
	})
	if err != nil {
		log.Printf("❌ Failed to upload test file: %v", err)
	} else {
		fmt.Printf("✅ Successfully uploaded test file: %s\n", testKey)
		
		// Test download
		_, err = client.GetObject(ctx, &s3.GetObjectInput{
			Bucket: aws.String(bucketName),
			Key:    aws.String(testKey),
		})
		if err != nil {
			log.Printf("❌ Failed to download test file: %v", err)
		} else {
			fmt.Println("✅ Successfully downloaded test file!")
		}

		// Clean up test file
		_, err = client.DeleteObject(ctx, &s3.DeleteObjectInput{
			Bucket: aws.String(bucketName),
			Key:    aws.String(testKey),
		})
		if err != nil {
			log.Printf("⚠️  Failed to delete test file: %v", err)
		} else {
			fmt.Println("✅ Test file cleaned up successfully!")
		}
	}

	fmt.Println()
	fmt.Println("🎉 R2 Bucket Setup Completed!")
	fmt.Println("=============================")
	fmt.Printf("Bucket '%s' is ready for use with xbit-cdn-service.\n", bucketName)
	fmt.Println()
	fmt.Println("Next steps:")
	fmt.Println("1. Run 'make test-r2-unstable' to verify the connection")
	fmt.Println("2. Start your application with 'make unstable-up'")
	fmt.Println("3. Check the application logs for any R2-related issues")
}

// Simple function to load environment variables from a file
func loadEnvFile(filename string) error {
	file, err := os.Open(filename)
	if err != nil {
		return err
	}
	defer file.Close()

	content := make([]byte, 0, 1024)
	buffer := make([]byte, 1024)
	for {
		n, err := file.Read(buffer)
		if n > 0 {
			content = append(content, buffer[:n]...)
		}
		if err != nil {
			break
		}
	}

	lines := strings.Split(string(content), "\n")
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" || strings.HasPrefix(line, "#") {
			continue
		}
		
		parts := strings.SplitN(line, "=", 2)
		if len(parts) == 2 {
			key := strings.TrimSpace(parts[0])
			value := strings.TrimSpace(parts[1])
			os.Setenv(key, value)
		}
	}

	return nil
}
