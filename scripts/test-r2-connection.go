package main

import (
	"context"
	"fmt"
	"log"
	"os"
	"strings"
	"time"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/credentials"
	"github.com/aws/aws-sdk-go-v2/service/s3"
)

func main() {
	fmt.Println("🚀 Testing Cloudflare R2 Connection...")
	fmt.Println("=====================================")

	// Load environment variables from .env.unstable
	if err := loadEnvFile("env/.env.unstable"); err != nil {
		log.Fatalf("❌ Failed to load environment file: %v", err)
	}

	// Get R2 configuration from environment
	accountID := os.Getenv("R2_ACCOUNT_ID")
	accessKeyID := os.Getenv("R2_ACCESS_KEY_ID")
	secretAccessKey := os.Getenv("R2_SECRET_ACCESS_KEY")
	bucketName := os.Getenv("R2_BUCKET_NAME")
	endpoint := os.Getenv("R2_ENDPOINT")

	// Validate configuration
	if accountID == "" || accessKeyID == "" || secretAccessKey == "" || bucketName == "" || endpoint == "" {
		log.Fatal("❌ Missing R2 configuration. Please check your .env.unstable file.")
	}

	fmt.Printf("📋 Configuration:\n")
	fmt.Printf("   Account ID: %s\n", accountID)
	fmt.Printf("   Access Key ID: %s\n", accessKeyID[:8]+"...")
	fmt.Printf("   Secret Access Key: %s\n", secretAccessKey[:8]+"...")
	fmt.Printf("   Bucket Name: %s\n", bucketName)
	fmt.Printf("   Endpoint: %s\n", endpoint)
	fmt.Println()

	// Create custom resolver for R2 endpoint
	customResolver := aws.EndpointResolverWithOptionsFunc(func(service, region string, options ...interface{}) (aws.Endpoint, error) {
		if service == s3.ServiceID {
			return aws.Endpoint{
				URL:           endpoint,
				SigningRegion: "auto",
			}, nil
		}
		return aws.Endpoint{}, fmt.Errorf("unknown endpoint requested")
	})

	// Load AWS config with custom credentials and endpoint
	awsCfg, err := config.LoadDefaultConfig(context.TODO(),
		config.WithCredentialsProvider(credentials.NewStaticCredentialsProvider(
			accessKeyID,
			secretAccessKey,
			"",
		)),
		config.WithEndpointResolverWithOptions(customResolver),
		config.WithRegion("auto"),
	)
	if err != nil {
		log.Fatalf("❌ Failed to load AWS config: %v", err)
	}

	// Create S3 client
	client := s3.NewFromConfig(awsCfg)

	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// Test 1: List buckets (to verify credentials)
	fmt.Println("🔍 Test 1: Verifying credentials...")
	_, err = client.ListBuckets(ctx, &s3.ListBucketsInput{})
	if err != nil {
		log.Printf("❌ Failed to list buckets: %v", err)
		log.Println("   This might be normal if your credentials don't have ListBuckets permission")
	} else {
		fmt.Println("✅ Credentials verified successfully!")
	}

	// Test 2: Check if bucket exists and is accessible
	fmt.Printf("🪣 Test 2: Checking bucket '%s' accessibility...\n", bucketName)
	_, err = client.HeadBucket(ctx, &s3.HeadBucketInput{
		Bucket: aws.String(bucketName),
	})
	if err != nil {
		log.Printf("❌ Failed to access bucket: %v", err)
		log.Println("   Please ensure the bucket exists and your credentials have access to it")
	} else {
		fmt.Println("✅ Bucket is accessible!")
	}

	// Test 3: Try to list objects in bucket
	fmt.Println("📁 Test 3: Listing objects in bucket...")
	listResult, err := client.ListObjectsV2(ctx, &s3.ListObjectsV2Input{
		Bucket:  aws.String(bucketName),
		MaxKeys: aws.Int32(5), // Limit to 5 objects for testing
	})
	if err != nil {
		log.Printf("❌ Failed to list objects: %v", err)
	} else {
		fmt.Printf("✅ Successfully listed objects! Found %d objects\n", len(listResult.Contents))
		if len(listResult.Contents) > 0 {
			fmt.Println("   Sample objects:")
			for i, obj := range listResult.Contents {
				if i >= 3 { // Show max 3 objects
					break
				}
				fmt.Printf("   - %s (Size: %d bytes, Modified: %s)\n", 
					*obj.Key, obj.Size, obj.LastModified.Format("2006-01-02 15:04:05"))
			}
		}
	}

	// Test 4: Test upload capability with a small test file
	fmt.Println("📤 Test 4: Testing upload capability...")
	testKey := fmt.Sprintf("test-connection-%d.txt", time.Now().Unix())
	testContent := "This is a test file created by xbit-cdn-service connection test"
	
	_, err = client.PutObject(ctx, &s3.PutObjectInput{
		Bucket:      aws.String(bucketName),
		Key:         aws.String(testKey),
		Body:        strings.NewReader(testContent),
		ContentType: aws.String("text/plain"),
	})
	if err != nil {
		log.Printf("❌ Failed to upload test file: %v", err)
	} else {
		fmt.Printf("✅ Successfully uploaded test file: %s\n", testKey)
		
		// Test 5: Test download capability
		fmt.Println("📥 Test 5: Testing download capability...")
		_, err = client.GetObject(ctx, &s3.GetObjectInput{
			Bucket: aws.String(bucketName),
			Key:    aws.String(testKey),
		})
		if err != nil {
			log.Printf("❌ Failed to download test file: %v", err)
		} else {
			fmt.Println("✅ Successfully downloaded test file!")
		}

		// Clean up: Delete test file
		fmt.Println("🧹 Cleaning up test file...")
		_, err = client.DeleteObject(ctx, &s3.DeleteObjectInput{
			Bucket: aws.String(bucketName),
			Key:    aws.String(testKey),
		})
		if err != nil {
			log.Printf("⚠️  Failed to delete test file: %v", err)
		} else {
			fmt.Println("✅ Test file cleaned up successfully!")
		}
	}

	fmt.Println()
	fmt.Println("🎉 R2 Connection Test Completed!")
	fmt.Println("================================")
	fmt.Println("If all tests passed, your R2 configuration is working correctly.")
	fmt.Println("You can now use the xbit-cdn-service with Cloudflare R2 in unstable environment.")
}

// Simple function to load environment variables from a file
func loadEnvFile(filename string) error {
	file, err := os.Open(filename)
	if err != nil {
		return err
	}
	defer file.Close()

	content := make([]byte, 0, 1024)
	buffer := make([]byte, 1024)
	for {
		n, err := file.Read(buffer)
		if n > 0 {
			content = append(content, buffer[:n]...)
		}
		if err != nil {
			break
		}
	}

	lines := strings.Split(string(content), "\n")
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" || strings.HasPrefix(line, "#") {
			continue
		}
		
		parts := strings.SplitN(line, "=", 2)
		if len(parts) == 2 {
			key := strings.TrimSpace(parts[0])
			value := strings.TrimSpace(parts[1])
			os.Setenv(key, value)
		}
	}

	return nil
}
