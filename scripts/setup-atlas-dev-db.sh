#!/bin/bash

# Setup Atlas development database for schema diffing (Local Development Only)
# This script creates a temporary database that Atlas uses for schema comparison
# Other environments (unstable, staging, production) are handled by <PERSON><PERSON><PERSON>

set -e

echo "Setting up Atlas development database for local environment..."

# Default PostgreSQL connection parameters
POSTGRES_HOST=${POSTGRES_HOST:-localhost}
POSTGRES_PORT=${POSTGRES_PORT:-5433}
POSTGRES_USER=${POSTGRES_USER:-postgres}
POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-password}

# Database name for Atlas dev environment
DEV_DB_LOCAL="atlas_dev_local"

# Function to create database if it doesn't exist
create_db_if_not_exists() {
    local db_name=$1
    echo "Creating database: $db_name"

    # Check if database exists
    if psql "postgresql://$POSTGRES_USER:$POSTGRES_PASSWORD@$POSTGRES_HOST:$POSTGRES_PORT/postgres" \
           -tAc "SELECT 1 FROM pg_database WHERE datname='$db_name'" | grep -q 1; then
        echo "Database $db_name already exists, dropping and recreating..."
        psql "postgresql://$POSTGRES_USER:$POSTGRES_PASSWORD@$POSTGRES_HOST:$POSTGRES_PORT/postgres" \
             -c "DROP DATABASE IF EXISTS $db_name;"
    fi

    psql "postgresql://$POSTGRES_USER:$POSTGRES_PASSWORD@$POSTGRES_HOST:$POSTGRES_PORT/postgres" \
         -c "CREATE DATABASE $db_name;"

    echo "Database $db_name created successfully"
}

# Create development database
create_db_if_not_exists $DEV_DB_LOCAL

echo "Atlas development database setup completed!"
echo ""
echo "You can now use the following commands:"
echo "  make db-diff   - Generate migration diff for local environment"
echo "  make db-apply  - Apply migrations for local environment"
