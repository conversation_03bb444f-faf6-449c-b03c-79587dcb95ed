package config

import (
	"os"
	"strconv"
	"strings"
	"time"
)

type Config struct {
	Server     ServerConfig
	Database   DatabaseConfig
	R2         R2Config
	CDN        CDNConfig
	JWT        JWTConfig
	Upload     UploadConfig
	Redis      RedisConfig
	CORS       CORSConfig
	Monitoring MonitoringConfig
	Security   SecurityConfig
	RateLimit  RateLimitConfig
	Backup     BackupConfig
	Logging    LoggingConfig
}

type ServerConfig struct {
	Port     string
	Host     string
	Env      string
	Debug    bool
	LogLevel string
}

type DatabaseConfig struct {
	Host               string
	Port               string
	User               string
	Password           string
	Name               string
	SSLMode            string
	MaxConnections     int
	MaxIdleConnections int
	ConnectionTimeout  time.Duration
	IdleTimeout        time.Duration
}

type R2Config struct {
	AccountID       string
	AccessKeyID     string
	SecretAccessKey string
	BucketName      string
	Endpoint        string
	Region          string
}

type CDNConfig struct {
	BaseURL  string
	ZoneID   string
	APIToken string
	CacheTTL int
}

type JWTConfig struct {
	Secret        string
	Expiry        time.Duration
	RefreshExpiry time.Duration
}

type UploadConfig struct {
	MaxFileSize       int64
	AllowedExtensions []string
	SignedURLExpiry   time.Duration
	TempDir           string
}

type RedisConfig struct {
	Host        string
	Port        string
	Password    string
	DB          int
	Enabled     bool
	ClusterMode bool
}

type CORSConfig struct {
	AllowedOrigins   []string
	AllowedMethods   []string
	AllowedHeaders   []string
	AllowCredentials bool
}

type MonitoringConfig struct {
	MetricsEnabled     bool
	HealthCheckEnabled bool
	ProfilingEnabled   bool
}

type SecurityConfig struct {
	EnableHTTPS    bool
	HSTSEnabled    bool
	CSRFProtection bool
	APIKeyRequired bool
}

type RateLimitConfig struct {
	Enabled           bool
	RequestsPerMinute int
	BurstSize         int
}

type BackupConfig struct {
	Enabled         bool
	Schedule        string
	RetentionDays   int
	S3BackupEnabled bool
}

type LoggingConfig struct {
	Structured bool
	Format     string
	Output     string
	FilePath   string
	MaxSize    string
	MaxBackups int
	MaxAge     int
}

func Load() *Config {
	return &Config{
		Server: ServerConfig{
			Port:     getEnv("PORT", "8080"),
			Host:     getEnv("HOST", "localhost"),
			Env:      getEnv("ENV", "development"),
			Debug:    parseBool(getEnv("DEBUG", "false")),
			LogLevel: getEnv("LOG_LEVEL", "info"),
		},
		Database: DatabaseConfig{
			Host:               getEnv("DB_HOST", "localhost"),
			Port:               getEnv("DB_PORT", "5432"),
			User:               getEnv("DB_USER", "postgres"),
			Password:           getEnv("DB_PASSWORD", ""),
			Name:               getEnv("DB_NAME", "xbit_cdn"),
			SSLMode:            getEnv("DB_SSLMODE", "disable"),
			MaxConnections:     parseInt(getEnv("DB_MAX_CONNECTIONS", "25")),
			MaxIdleConnections: parseInt(getEnv("DB_MAX_IDLE_CONNECTIONS", "10")),
			ConnectionTimeout:  parseDuration(getEnv("DB_CONNECTION_TIMEOUT", "30s")),
			IdleTimeout:        parseDuration(getEnv("DB_IDLE_TIMEOUT", "10m")),
		},
		R2: R2Config{
			AccountID:       getEnv("R2_ACCOUNT_ID", ""),
			AccessKeyID:     getEnv("R2_ACCESS_KEY_ID", ""),
			SecretAccessKey: getEnv("R2_SECRET_ACCESS_KEY", ""),
			BucketName:      getEnv("R2_BUCKET_NAME", ""),
			Endpoint:        getEnv("R2_ENDPOINT", ""),
			Region:          getEnv("R2_REGION", "auto"),
		},
		CDN: CDNConfig{
			BaseURL:  getEnv("CDN_BASE_URL", ""),
			ZoneID:   getEnv("CDN_ZONE_ID", ""),
			APIToken: getEnv("CDN_API_TOKEN", ""),
			CacheTTL: parseInt(getEnv("CDN_CACHE_TTL", "3600")),
		},
		JWT: JWTConfig{
			Secret:        getEnv("JWT_SECRET", "default-secret"),
			Expiry:        parseDuration(getEnv("JWT_EXPIRY", "24h")),
			RefreshExpiry: parseDuration(getEnv("JWT_REFRESH_EXPIRY", "168h")),
		},
		Upload: UploadConfig{
			MaxFileSize:       parseSize(getEnv("MAX_FILE_SIZE", "100MB")),
			AllowedExtensions: parseStringSlice(getEnv("ALLOWED_EXTENSIONS", "jpg,jpeg,png,gif,mp4,mov,avi,webm")),
			SignedURLExpiry:   parseDuration(getEnv("SIGNED_URL_EXPIRY", "1h")),
			TempDir:           getEnv("TEMP_DIR", "/tmp/xbit-uploads"),
		},
		Redis: RedisConfig{
			Host:        getEnv("REDIS_HOST", "localhost"),
			Port:        getEnv("REDIS_PORT", "6379"),
			Password:    getEnv("REDIS_PASSWORD", ""),
			DB:          parseInt(getEnv("REDIS_DB", "0")),
			Enabled:     parseBool(getEnv("REDIS_ENABLED", "true")),
			ClusterMode: parseBool(getEnv("REDIS_CLUSTER_MODE", "false")),
		},
		CORS: CORSConfig{
			AllowedOrigins:   parseStringSlice(getEnv("CORS_ALLOWED_ORIGINS", "http://localhost:3000")),
			AllowedMethods:   parseStringSlice(getEnv("CORS_ALLOWED_METHODS", "GET,POST,PUT,DELETE,OPTIONS")),
			AllowedHeaders:   parseStringSlice(getEnv("CORS_ALLOWED_HEADERS", "*")),
			AllowCredentials: parseBool(getEnv("CORS_ALLOW_CREDENTIALS", "true")),
		},
		Monitoring: MonitoringConfig{
			MetricsEnabled:     parseBool(getEnv("METRICS_ENABLED", "true")),
			HealthCheckEnabled: parseBool(getEnv("HEALTH_CHECK_ENABLED", "true")),
			ProfilingEnabled:   parseBool(getEnv("PROFILING_ENABLED", "false")),
		},
		Security: SecurityConfig{
			EnableHTTPS:    parseBool(getEnv("ENABLE_HTTPS", "false")),
			HSTSEnabled:    parseBool(getEnv("HSTS_ENABLED", "false")),
			CSRFProtection: parseBool(getEnv("CSRF_PROTECTION", "false")),
			APIKeyRequired: parseBool(getEnv("API_KEY_REQUIRED", "false")),
		},
		RateLimit: RateLimitConfig{
			Enabled:           parseBool(getEnv("RATE_LIMITING_ENABLED", "false")),
			RequestsPerMinute: parseInt(getEnv("REQUESTS_PER_MINUTE", "100")),
			BurstSize:         parseInt(getEnv("BURST_SIZE", "10")),
		},
		Backup: BackupConfig{
			Enabled:         parseBool(getEnv("BACKUP_ENABLED", "false")),
			Schedule:        getEnv("BACKUP_SCHEDULE", "0 2 * * *"),
			RetentionDays:   parseInt(getEnv("BACKUP_RETENTION_DAYS", "30")),
			S3BackupEnabled: parseBool(getEnv("S3_BACKUP_ENABLED", "false")),
		},
		Logging: LoggingConfig{
			Structured: parseBool(getEnv("LOG_STRUCTURED", "false")),
			Format:     getEnv("LOG_FORMAT", "text"),
			Output:     getEnv("LOG_OUTPUT", "stdout"),
			FilePath:   getEnv("LOG_FILE_PATH", "/var/log/xbit-cdn/app.log"),
			MaxSize:    getEnv("LOG_MAX_SIZE", "100MB"),
			MaxBackups: parseInt(getEnv("LOG_MAX_BACKUPS", "10")),
			MaxAge:     parseInt(getEnv("LOG_MAX_AGE", "30")),
		},
	}
}

func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

func parseDuration(s string) time.Duration {
	d, err := time.ParseDuration(s)
	if err != nil {
		return time.Hour
	}
	return d
}

func parseInt(s string) int {
	i, err := strconv.Atoi(s)
	if err != nil {
		return 0
	}
	return i
}

func parseBool(s string) bool {
	b, err := strconv.ParseBool(s)
	if err != nil {
		return false
	}
	return b
}

func parseStringSlice(s string) []string {
	if s == "" {
		return []string{}
	}
	return strings.Split(s, ",")
}

func parseSize(s string) int64 {
	// Simple parser for sizes like "100MB"
	if len(s) < 3 {
		return 100 * 1024 * 1024 // Default 100MB
	}

	unit := s[len(s)-2:]
	numStr := s[:len(s)-2]
	num, err := strconv.ParseInt(numStr, 10, 64)
	if err != nil {
		return 100 * 1024 * 1024
	}

	switch unit {
	case "KB":
		return num * 1024
	case "MB":
		return num * 1024 * 1024
	case "GB":
		return num * 1024 * 1024 * 1024
	default:
		return num
	}
}
