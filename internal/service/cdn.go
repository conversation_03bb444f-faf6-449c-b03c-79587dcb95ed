package service

import (
	"context"
	"fmt"
	"strings"
	"time"

	"xbit-cdn-service/internal/config"
)

type CDNService struct {
	config *config.Config
}

func NewCDNService(cfg *config.Config) (*CDNService, error) {
	if cfg.CDN.BaseURL == "" {
		// CDN service is optional, return nil if not configured
		return nil, nil
	}

	return &CDNService{
		config: cfg,
	}, nil
}

// PurgeCache purges cache for specific files
func (c *CDNService) PurgeCache(ctx context.Context, urls []string) error {
	if c.config.CDN.APIToken == "" || c.config.CDN.ZoneID == "" {
		// CDN purging is optional, just log and continue
		fmt.Printf("CDN cache purge skipped - not configured\n")
		return nil
	}

	// TODO: Implement actual Cloudflare API call for cache purging
	// For now, just log the operation
	fmt.Printf("CDN cache purge requested for URLs: %v\n", urls)
	return nil
}

// PurgeCacheByTags purges cache by tags
func (c *CDNService) PurgeCacheByTags(ctx context.Context, tags []string) error {
	if c.config.CDN.APIToken == "" || c.config.CDN.ZoneID == "" {
		// CDN purging is optional, just log and continue
		fmt.Printf("CDN purge by tags skipped - not configured\n")
		return nil
	}

	// TODO: Implement actual Cloudflare API call for cache purging by tags
	// For now, just log the operation
	fmt.Printf("CDN cache purge by tags requested: %v\n", tags)
	return nil
}

// SetCachingRules creates page rules for optimal caching
func (c *CDNService) SetCachingRules(ctx context.Context) error {
	if c.config.CDN.APIToken == "" || c.config.CDN.ZoneID == "" {
		fmt.Printf("CDN caching rules setup skipped - not configured\n")
		return nil
	}

	// TODO: Implement actual Cloudflare API calls for setting caching rules
	// For now, just log the operation
	fmt.Printf("CDN caching rules setup requested for zone: %s\n", c.config.CDN.ZoneID)
	
	// Log recommended caching rules
	fmt.Printf("Recommended caching rules:\n")
	fmt.Printf("- Images (*.jpg, *.png, *.gif): Cache for 1 year\n")
	fmt.Printf("- Videos (*.mp4, *.mov): Cache for 1 month\n")
	fmt.Printf("- API endpoints (/graphql): No cache\n")
	
	return nil
}

// OptimizeImages enables image optimization features
func (c *CDNService) OptimizeImages(ctx context.Context) error {
	if c.config.CDN.APIToken == "" || c.config.CDN.ZoneID == "" {
		fmt.Printf("CDN image optimization skipped - not configured\n")
		return nil
	}

	// TODO: Implement actual Cloudflare API calls for image optimization
	// For now, just log the operation
	fmt.Printf("CDN image optimization requested for zone: %s\n", c.config.CDN.ZoneID)
	fmt.Printf("Recommended settings:\n")
	fmt.Printf("- Polish: lossless compression\n")
	fmt.Printf("- WebP conversion: enabled\n")
	
	return nil
}

// SetSecurityHeaders configures security headers
func (c *CDNService) SetSecurityHeaders(ctx context.Context) error {
	if c.config.CDN.APIToken == "" || c.config.CDN.ZoneID == "" {
		fmt.Printf("CDN security headers setup skipped - not configured\n")
		return nil
	}

	// TODO: Implement actual Cloudflare API calls for security headers
	// For now, just log the operation
	fmt.Printf("CDN security headers setup requested for zone: %s\n", c.config.CDN.ZoneID)
	fmt.Printf("Recommended security headers:\n")
	fmt.Printf("- Strict-Transport-Security: max-age=31536000; includeSubDomains\n")
	fmt.Printf("- X-Content-Type-Options: nosniff\n")
	fmt.Printf("- Referrer-Policy: strict-origin-when-cross-origin\n")
	
	return nil
}

// GetCDNURL transforms R2 URL to CDN URL
func (c *CDNService) GetCDNURL(r2URL string) string {
	if c.config.CDN.BaseURL == "" {
		return r2URL
	}

	// Extract filename from R2 URL
	parts := strings.Split(r2URL, "/")
	if len(parts) == 0 {
		return r2URL
	}

	filename := parts[len(parts)-1]
	return fmt.Sprintf("%s/%s", c.config.CDN.BaseURL, filename)
}

// GetOptimizedImageURL returns URL with image optimization parameters
func (c *CDNService) GetOptimizedImageURL(baseURL string, options ImageOptimizationOptions) string {
	if c.config.CDN.BaseURL == "" {
		return baseURL
	}

	params := []string{}

	if options.Width > 0 {
		params = append(params, fmt.Sprintf("w=%d", options.Width))
	}

	if options.Height > 0 {
		params = append(params, fmt.Sprintf("h=%d", options.Height))
	}

	if options.Quality > 0 && options.Quality <= 100 {
		params = append(params, fmt.Sprintf("q=%d", options.Quality))
	}

	if options.Format != "" {
		params = append(params, fmt.Sprintf("f=%s", options.Format))
	}

	if options.Fit != "" {
		params = append(params, fmt.Sprintf("fit=%s", options.Fit))
	}

	if len(params) == 0 {
		return baseURL
	}

	separator := "?"
	if strings.Contains(baseURL, "?") {
		separator = "&"
	}

	return fmt.Sprintf("%s%s%s", baseURL, separator, strings.Join(params, "&"))
}

// ImageOptimizationOptions defines image transformation options
type ImageOptimizationOptions struct {
	Width   int    `json:"width,omitempty"`
	Height  int    `json:"height,omitempty"`
	Quality int    `json:"quality,omitempty"`
	Format  string `json:"format,omitempty"` // webp, jpeg, png
	Fit     string `json:"fit,omitempty"`    // scale-down, contain, cover, crop, pad
}

// GetAnalytics retrieves CDN analytics data
func (c *CDNService) GetAnalytics(ctx context.Context, since, until time.Time) (*CDNAnalytics, error) {
	if c.config.CDN.APIToken == "" || c.config.CDN.ZoneID == "" {
		return nil, fmt.Errorf("CDN service not configured")
	}

	// TODO: Implement actual Cloudflare API calls for analytics
	// For now, return mock data
	return &CDNAnalytics{
		Requests:  1000,
		Bandwidth: 1024000,
		CacheHits: 800,
		CacheMiss: 200,
		Threats:   0,
		PageViews: 500,
		Uniques:   100,
	}, nil
}

// CDNAnalytics represents CDN analytics data
type CDNAnalytics struct {
	Requests  int `json:"requests"`
	Bandwidth int `json:"bandwidth"`
	CacheHits int `json:"cache_hits"`
	CacheMiss int `json:"cache_miss"`
	Threats   int `json:"threats"`
	PageViews int `json:"page_views"`
	Uniques   int `json:"uniques"`
}

// SetupCDN initializes all CDN configurations
func (c *CDNService) SetupCDN(ctx context.Context) error {
	if c.config.CDN.APIToken == "" || c.config.CDN.ZoneID == "" {
		fmt.Printf("CDN setup skipped - not configured\n")
		return nil
	}

	fmt.Printf("Setting up CDN for zone: %s\n", c.config.CDN.ZoneID)

	// Set caching rules
	if err := c.SetCachingRules(ctx); err != nil {
		return fmt.Errorf("failed to set caching rules: %w", err)
	}

	// Optimize images
	if err := c.OptimizeImages(ctx); err != nil {
		return fmt.Errorf("failed to optimize images: %w", err)
	}

	// Set security headers
	if err := c.SetSecurityHeaders(ctx); err != nil {
		return fmt.Errorf("failed to set security headers: %w", err)
	}

	fmt.Printf("CDN setup completed successfully\n")
	return nil
}
