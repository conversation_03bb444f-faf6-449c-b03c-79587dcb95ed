package model

import (
	"database/sql"
	"time"

	"github.com/lib/pq"
)

// Schema represents the database schema for Atlas migrations
// This file defines the desired database schema based on Go models

// FilesTable represents the files table structure for Atlas
type FilesTable struct {
	ID           string         `db:"id" atlas:"type:varchar(36),primary_key"`
	Filename     string         `db:"filename" atlas:"type:varchar(255),not_null,unique"`
	OriginalName string         `db:"original_name" atlas:"type:varchar(255),not_null"`
	FileType     string         `db:"file_type" atlas:"type:varchar(20),not_null,check:file_type IN ('IMAGE', 'VIDEO')"`
	MimeType     string         `db:"mime_type" atlas:"type:varchar(100),not_null"`
	Size         int64          `db:"size" atlas:"type:bigint,not_null"`
	Status       string         `db:"status" atlas:"type:varchar(20),not_null,default:'UPLOADING',check:status IN ('UPLOADING', 'PROCESSING', 'READY', 'ERROR')"`
	UploadedAt   time.Time      `db:"uploaded_at" atlas:"type:timestamptz,not_null,default:NOW()"`
	UpdatedAt    time.Time      `db:"updated_at" atlas:"type:timestamptz,not_null,default:NOW()"`
	PublicURL    sql.NullString `db:"public_url" atlas:"type:text"`
	CdnURL       sql.NullString `db:"cdn_url" atlas:"type:text"`
	Tags         pq.StringArray `db:"tags" atlas:"type:text[],default:'{}'"`
	Metadata     sql.NullString `db:"metadata" atlas:"type:jsonb"`
	UserID       sql.NullString `db:"user_id" atlas:"type:varchar(36)"`
}

// TableName returns the table name for Atlas
func (FilesTable) TableName() string {
	return "files"
}

// Indexes defines the indexes for the files table
func (FilesTable) Indexes() []string {
	return []string{
		"CREATE INDEX IF NOT EXISTS idx_files_file_type ON files(file_type)",
		"CREATE INDEX IF NOT EXISTS idx_files_status ON files(status)",
		"CREATE INDEX IF NOT EXISTS idx_files_uploaded_at ON files(uploaded_at)",
		"CREATE INDEX IF NOT EXISTS idx_files_user_id ON files(user_id)",
		"CREATE INDEX IF NOT EXISTS idx_files_tags ON files USING GIN(tags)",
		"CREATE INDEX IF NOT EXISTS idx_files_metadata ON files USING GIN(metadata)",
	}
}

// Triggers defines the triggers for the files table
func (FilesTable) Triggers() []string {
	return []string{
		`CREATE OR REPLACE FUNCTION update_updated_at_column()
		RETURNS TRIGGER AS $$
		BEGIN
			NEW.updated_at = NOW();
			RETURN NEW;
		END;
		$$ language 'plpgsql'`,
		`CREATE TRIGGER update_files_updated_at 
			BEFORE UPDATE ON files 
			FOR EACH ROW 
			EXECUTE FUNCTION update_updated_at_column()`,
	}
}
