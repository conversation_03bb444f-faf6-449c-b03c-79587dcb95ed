package model

import (
	"time"
)

// FileType represents the type of file
type FileType string

const (
	FileTypeImage FileType = "IMAGE"
	FileTypeVideo FileType = "VIDEO"
)

// FileStatus represents the status of file processing
type FileStatus string

const (
	FileStatusUploading  FileStatus = "UPLOADING"
	FileStatusProcessing FileStatus = "PROCESSING"
	FileStatusReady      FileStatus = "READY"
	FileStatusError      FileStatus = "ERROR"
)

// URLType represents the type of URL to generate
type URLType string

const (
	URLTypePublic URLType = "PUBLIC"
	URLTypeSigned URLType = "SIGNED"
)

// FileMetadata represents file information stored in database
type FileMetadata struct {
	ID           string     `json:"id" db:"id"`
	Filename     string     `json:"filename" db:"filename"`
	OriginalName string     `json:"originalName" db:"original_name"`
	FileType     FileType   `json:"fileType" db:"file_type"`
	MimeType     string     `json:"mimeType" db:"mime_type"`
	Size         int64      `json:"size" db:"size"`
	Status       FileStatus `json:"status" db:"status"`
	UploadedAt   time.Time  `json:"uploadedAt" db:"uploaded_at"`
	UpdatedAt    time.Time  `json:"updatedAt" db:"updated_at"`
	PublicURL    *string    `json:"publicURL,omitempty" db:"public_url"`
	CdnURL       *string    `json:"cdnURL,omitempty" db:"cdn_url"`
	Tags         []string   `json:"tags,omitempty" db:"tags"`
	Metadata     *string    `json:"metadata,omitempty" db:"metadata"`
	UserID       *string    `json:"userID,omitempty" db:"user_id"`
}

// UploadInput represents input for file upload
type UploadInput struct {
	Filename        string   `json:"filename"`
	FileType        FileType `json:"fileType"`
	MimeType        string   `json:"mimeType"`
	Size            int64    `json:"size"`
	Tags            []string `json:"tags,omitempty"`
	Metadata        *string  `json:"metadata,omitempty"`
	UseSignedUpload bool     `json:"useSignedUpload"`
}

// UploadResponse represents response from upload operation
type UploadResponse struct {
	Success   bool          `json:"success"`
	Message   string        `json:"message"`
	File      *FileMetadata `json:"file,omitempty"`
	UploadURL *string       `json:"uploadURL,omitempty"`
}

// DownloadResponse represents response from download URL generation
type DownloadResponse struct {
	Success   bool    `json:"success"`
	Message   string  `json:"message"`
	URL       *string `json:"url,omitempty"`
	ExpiresAt *string `json:"expiresAt,omitempty"`
}

// FileFilter represents filters for file listing
type FileFilter struct {
	FileType       *FileType   `json:"fileType,omitempty"`
	Status         *FileStatus `json:"status,omitempty"`
	Tags           []string    `json:"tags,omitempty"`
	UploadedAfter  *string     `json:"uploadedAfter,omitempty"`
	UploadedBefore *string     `json:"uploadedBefore,omitempty"`
	UserID         *string     `json:"userID,omitempty"`
}

// PaginationInput represents pagination parameters
type PaginationInput struct {
	Limit  int `json:"limit"`
	Offset int `json:"offset"`
}

// FileListResponse represents response from file listing
type FileListResponse struct {
	Files           []*FileMetadata `json:"files"`
	TotalCount      int             `json:"totalCount"`
	HasNextPage     bool            `json:"hasNextPage"`
	HasPreviousPage bool            `json:"hasPreviousPage"`
}

// IsValidFileType checks if the file type is valid
func (ft FileType) IsValid() bool {
	switch ft {
	case FileTypeImage, FileTypeVideo:
		return true
	default:
		return false
	}
}

// IsValidStatus checks if the file status is valid
func (fs FileStatus) IsValid() bool {
	switch fs {
	case FileStatusUploading, FileStatusProcessing, FileStatusReady, FileStatusError:
		return true
	default:
		return false
	}
}

// IsValidURLType checks if the URL type is valid
func (ut URLType) IsValid() bool {
	switch ut {
	case URLTypePublic, URLTypeSigned:
		return true
	default:
		return false
	}
}
