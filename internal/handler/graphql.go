package handler

import (
	"context"
	"fmt"

	"xbit-cdn-service/internal/model"
	"xbit-cdn-service/internal/service"
	"xbit-cdn-service/pkg/auth"
)

type GraphQLHandler struct {
	fileService *service.FileService
}

func NewGraphQLHandler(fileService *service.FileService) *GraphQLHandler {
	return &GraphQLHandler{
		fileService: fileService,
	}
}

// Query resolvers

func (h *GraphQLHandler) Health(ctx context.Context) (string, error) {
	return "OK", nil
}

func (h *GraphQLHandler) File(ctx context.Context, id string) (*model.FileMetadata, error) {
	return h.fileService.GetFile(ctx, id)
}

func (h *GraphQLHandler) Files(ctx context.Context, filter *model.FileFilter, pagination *model.PaginationInput) (*model.FileListResponse, error) {
	// Check authentication and permissions
	claims, ok := auth.GetClaimsFromContext(ctx)
	if !ok {
		return &model.FileListResponse{
			Files:           []*model.FileMetadata{},
			TotalCount:      0,
			HasNextPage:     false,
			HasPreviousPage: false,
		}, fmt.Errorf("authentication required")
	}

	if !claims.CanReadFiles() {
		return &model.FileListResponse{
			Files:           []*model.FileMetadata{},
			TotalCount:      0,
			HasNextPage:     false,
			HasPreviousPage: false,
		}, fmt.Errorf("insufficient permissions to read files")
	}

	// Non-admin users can only see their own files
	if !claims.IsAdmin() {
		if filter == nil {
			filter = &model.FileFilter{}
		}
		filter.UserID = &claims.UserID
	}

	return h.fileService.ListFiles(ctx, filter, pagination)
}

func (h *GraphQLHandler) DownloadURL(ctx context.Context, id string, urlType *model.URLType, expiresIn *int) (*model.DownloadResponse, error) {
	// Default to public URL if not specified
	if urlType == nil {
		defaultType := model.URLTypePublic
		urlType = &defaultType
	}

	return h.fileService.GenerateDownloadURL(ctx, id, *urlType, expiresIn)
}

// Mutation resolvers

func (h *GraphQLHandler) UploadFile(ctx context.Context, input model.UploadInput) (*model.UploadResponse, error) {
	// Check authentication and permissions
	claims, ok := auth.GetClaimsFromContext(ctx)
	if !ok {
		return &model.UploadResponse{
			Success: false,
			Message: "Authentication required",
		}, nil
	}

	if !claims.CanWriteFiles() {
		return &model.UploadResponse{
			Success: false,
			Message: "Insufficient permissions to upload files",
		}, nil
	}

	// Extract user ID from context
	userID := claims.UserID

	return h.fileService.UploadFile(ctx, &input, &userID)
}

func (h *GraphQLHandler) CompleteUpload(ctx context.Context, id string) (*model.FileMetadata, error) {
	return h.fileService.CompleteUpload(ctx, id)
}

func (h *GraphQLHandler) DeleteFile(ctx context.Context, id string) (bool, error) {
	// Check authentication and permissions
	claims, ok := auth.GetClaimsFromContext(ctx)
	if !ok {
		return false, fmt.Errorf("authentication required")
	}

	if !claims.CanDeleteFiles() {
		return false, fmt.Errorf("insufficient permissions to delete files")
	}

	// Additional check: users can only delete their own files unless they're admin
	if !claims.IsAdmin() {
		file, err := h.fileService.GetFile(ctx, id)
		if err != nil {
			return false, err
		}
		if file == nil {
			return false, fmt.Errorf("file not found")
		}
		if file.UserID == nil || *file.UserID != claims.UserID {
			return false, fmt.Errorf("insufficient permissions to delete this file")
		}
	}

	return h.fileService.DeleteFile(ctx, id)
}

func (h *GraphQLHandler) UpdateFileMetadata(ctx context.Context, id string, tags []string, metadata *string) (*model.FileMetadata, error) {
	return h.fileService.UpdateFileMetadata(ctx, id, tags, metadata)
}

// Helper functions

func (h *GraphQLHandler) getUserIDFromContext(ctx context.Context) *string {
	// TODO: Implement JWT token parsing to extract user ID
	// For now, return nil (no authentication)
	return nil
}

func (h *GraphQLHandler) validatePermissions(ctx context.Context, fileID string) error {
	// TODO: Implement permission checking
	// Check if user has access to the file
	return nil
}

// Custom scalar resolvers (if needed)

func (h *GraphQLHandler) MarshalFileType(ft model.FileType) (string, error) {
	return string(ft), nil
}

func (h *GraphQLHandler) UnmarshalFileType(v interface{}) (model.FileType, error) {
	switch s := v.(type) {
	case string:
		return model.FileType(s), nil
	case *string:
		return model.FileType(*s), nil
	default:
		return "", fmt.Errorf("invalid FileType: %v", v)
	}
}

func (h *GraphQLHandler) MarshalFileStatus(fs model.FileStatus) (string, error) {
	return string(fs), nil
}

func (h *GraphQLHandler) UnmarshalFileStatus(v interface{}) (model.FileStatus, error) {
	switch s := v.(type) {
	case string:
		return model.FileStatus(s), nil
	case *string:
		return model.FileStatus(*s), nil
	default:
		return "", fmt.Errorf("invalid FileStatus: %v", v)
	}
}

func (h *GraphQLHandler) MarshalURLType(ut model.URLType) (string, error) {
	return string(ut), nil
}

func (h *GraphQLHandler) UnmarshalURLType(v interface{}) (model.URLType, error) {
	switch s := v.(type) {
	case string:
		return model.URLType(s), nil
	case *string:
		return model.URLType(*s), nil
	default:
		return "", fmt.Errorf("invalid URLType: %v", v)
	}
}
