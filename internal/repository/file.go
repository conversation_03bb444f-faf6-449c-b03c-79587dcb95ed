package repository

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/google/uuid"
	"github.com/lib/pq"
	_ "github.com/lib/pq"

	"xbit-cdn-service/internal/model"
)

type FileRepository struct {
	db *sql.DB
}

func NewFileRepository(db *sql.DB) *FileRepository {
	return &FileRepository{db: db}
}

// Create creates a new file record
func (r *FileRepository) Create(ctx context.Context, file *model.FileMetadata) error {
	if file.ID == "" {
		file.ID = uuid.New().String()
	}
	
	now := time.Now()
	file.UploadedAt = now
	file.UpdatedAt = now

	query := `
		INSERT INTO files (id, filename, original_name, file_type, mime_type, size, status, 
			uploaded_at, updated_at, public_url, cdn_url, tags, metadata, user_id)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14)
	`
	
	_, err := r.db.ExecContext(ctx, query,
		file.ID, file.Filename, file.OriginalName, file.FileType, file.MimeType,
		file.Size, file.Status, file.UploadedAt, file.UpdatedAt,
		file.PublicURL, file.CdnURL, pq.Array(file.Tags), file.Metadata, file.UserID,
	)
	if err != nil {
		return fmt.Errorf("failed to create file record: %w", err)
	}
	
	return nil
}

// GetByID retrieves a file by ID
func (r *FileRepository) GetByID(ctx context.Context, id string) (*model.FileMetadata, error) {
	query := `
		SELECT id, filename, original_name, file_type, mime_type, size, status,
			uploaded_at, updated_at, public_url, cdn_url, tags, metadata, user_id
		FROM files WHERE id = $1
	`
	
	file := &model.FileMetadata{}
	var tags pq.StringArray
	
	err := r.db.QueryRowContext(ctx, query, id).Scan(
		&file.ID, &file.Filename, &file.OriginalName, &file.FileType, &file.MimeType,
		&file.Size, &file.Status, &file.UploadedAt, &file.UpdatedAt,
		&file.PublicURL, &file.CdnURL, &tags, &file.Metadata, &file.UserID,
	)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get file by ID: %w", err)
	}
	
	file.Tags = []string(tags)
	return file, nil
}

// Update updates a file record
func (r *FileRepository) Update(ctx context.Context, file *model.FileMetadata) error {
	file.UpdatedAt = time.Now()
	
	query := `
		UPDATE files SET filename = $2, original_name = $3, file_type = $4, mime_type = $5,
			size = $6, status = $7, updated_at = $8, public_url = $9, cdn_url = $10,
			tags = $11, metadata = $12, user_id = $13
		WHERE id = $1
	`
	
	result, err := r.db.ExecContext(ctx, query,
		file.ID, file.Filename, file.OriginalName, file.FileType, file.MimeType,
		file.Size, file.Status, file.UpdatedAt, file.PublicURL, file.CdnURL,
		pq.Array(file.Tags), file.Metadata, file.UserID,
	)
	if err != nil {
		return fmt.Errorf("failed to update file: %w", err)
	}
	
	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("failed to get rows affected: %w", err)
	}
	
	if rowsAffected == 0 {
		return fmt.Errorf("file not found")
	}
	
	return nil
}

// Delete deletes a file record
func (r *FileRepository) Delete(ctx context.Context, id string) error {
	query := `DELETE FROM files WHERE id = $1`
	
	result, err := r.db.ExecContext(ctx, query, id)
	if err != nil {
		return fmt.Errorf("failed to delete file: %w", err)
	}
	
	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("failed to get rows affected: %w", err)
	}
	
	if rowsAffected == 0 {
		return fmt.Errorf("file not found")
	}
	
	return nil
}

// List retrieves files with filtering and pagination
func (r *FileRepository) List(ctx context.Context, filter *model.FileFilter, pagination *model.PaginationInput) (*model.FileListResponse, error) {
	// Build WHERE clause
	var conditions []string
	var args []interface{}
	argIndex := 1

	if filter != nil {
		if filter.FileType != nil {
			conditions = append(conditions, fmt.Sprintf("file_type = $%d", argIndex))
			args = append(args, *filter.FileType)
			argIndex++
		}
		
		if filter.Status != nil {
			conditions = append(conditions, fmt.Sprintf("status = $%d", argIndex))
			args = append(args, *filter.Status)
			argIndex++
		}
		
		if filter.UserID != nil {
			conditions = append(conditions, fmt.Sprintf("user_id = $%d", argIndex))
			args = append(args, *filter.UserID)
			argIndex++
		}
		
		if len(filter.Tags) > 0 {
			conditions = append(conditions, fmt.Sprintf("tags && $%d", argIndex))
			args = append(args, pq.Array(filter.Tags))
			argIndex++
		}
		
		if filter.UploadedAfter != nil {
			if uploadedAfter, err := time.Parse(time.RFC3339, *filter.UploadedAfter); err == nil {
				conditions = append(conditions, fmt.Sprintf("uploaded_at >= $%d", argIndex))
				args = append(args, uploadedAfter)
				argIndex++
			}
		}
		
		if filter.UploadedBefore != nil {
			if uploadedBefore, err := time.Parse(time.RFC3339, *filter.UploadedBefore); err == nil {
				conditions = append(conditions, fmt.Sprintf("uploaded_at <= $%d", argIndex))
				args = append(args, uploadedBefore)
				argIndex++
			}
		}
	}

	whereClause := ""
	if len(conditions) > 0 {
		whereClause = "WHERE " + strings.Join(conditions, " AND ")
	}

	// Count total records
	countQuery := fmt.Sprintf("SELECT COUNT(*) FROM files %s", whereClause)
	var totalCount int
	err := r.db.QueryRowContext(ctx, countQuery, args...).Scan(&totalCount)
	if err != nil {
		return nil, fmt.Errorf("failed to count files: %w", err)
	}

	// Get files with pagination
	query := fmt.Sprintf(`
		SELECT id, filename, original_name, file_type, mime_type, size, status,
			uploaded_at, updated_at, public_url, cdn_url, tags, metadata, user_id
		FROM files %s
		ORDER BY uploaded_at DESC
		LIMIT $%d OFFSET $%d
	`, whereClause, argIndex, argIndex+1)
	
	args = append(args, pagination.Limit, pagination.Offset)
	
	rows, err := r.db.QueryContext(ctx, query, args...)
	if err != nil {
		return nil, fmt.Errorf("failed to query files: %w", err)
	}
	defer rows.Close()

	var files []*model.FileMetadata
	for rows.Next() {
		file := &model.FileMetadata{}
		var tags pq.StringArray
		
		err := rows.Scan(
			&file.ID, &file.Filename, &file.OriginalName, &file.FileType, &file.MimeType,
			&file.Size, &file.Status, &file.UploadedAt, &file.UpdatedAt,
			&file.PublicURL, &file.CdnURL, &tags, &file.Metadata, &file.UserID,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan file row: %w", err)
		}
		
		file.Tags = []string(tags)
		files = append(files, file)
	}

	if err = rows.Err(); err != nil {
		return nil, fmt.Errorf("error iterating file rows: %w", err)
	}

	// Calculate pagination info
	hasNextPage := pagination.Offset+len(files) < totalCount
	hasPreviousPage := pagination.Offset > 0

	return &model.FileListResponse{
		Files:           files,
		TotalCount:      totalCount,
		HasNextPage:     hasNextPage,
		HasPreviousPage: hasPreviousPage,
	}, nil
}
