# GraphQL schema definition for XBIT CDN Service

# File types
enum FileType {
  IMAGE
  VIDEO
}

# File status
enum FileStatus {
  UPLOADING
  PROCESSING
  READY
  ERROR
}

# URL types
enum URLType {
  PUBLIC
  SIGNED
}

# File metadata
type FileMetadata {
  id: ID!
  filename: String!
  originalName: String!
  fileType: FileType!
  mimeType: String!
  size: Int!
  status: FileStatus!
  uploadedAt: String!
  updatedAt: String!
  publicURL: String
  cdnURL: String
  tags: [String!]
  metadata: String
}

# Upload response
type UploadResponse {
  success: Boolean!
  message: String!
  file: FileMetadata
  uploadURL: String
}

# Download response
type DownloadResponse {
  success: Boolean!
  message: String!
  url: String
  expiresAt: String
}

# File list response
type FileListResponse {
  files: [FileMetadata!]!
  totalCount: Int!
  hasNextPage: Boolean!
  hasPreviousPage: Boolean!
}

# Input types
input UploadInput {
  filename: String!
  fileType: FileType!
  mimeType: String!
  size: Int!
  tags: [String!]
  metadata: String
  useSignedUpload: Boolean = false
}

input FileFilter {
  fileType: FileType
  status: FileStatus
  tags: [String!]
  uploadedAfter: String
  uploadedBefore: String
}

input PaginationInput {
  limit: Int = 20
  offset: Int = 0
}

# Root types
type Query {
  file(id: ID!): FileMetadata
  files(filter: FileFilter, pagination: PaginationInput): FileListResponse!
  downloadURL(id: ID!, urlType: URLType = PUBLIC, expiresIn: Int): DownloadResponse!
  health: String!
}

type Mutation {
  uploadFile(input: UploadInput!): UploadResponse!
  completeUpload(id: ID!): FileMetadata
  deleteFile(id: ID!): Boolean!
  updateFileMetadata(id: ID!, tags: [String!], metadata: String): FileMetadata
}
