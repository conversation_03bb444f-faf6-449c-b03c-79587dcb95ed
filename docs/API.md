# API Documentation

## GraphQL API

The XBIT CDN Service provides a GraphQL API for all file operations. The API is available at `/graphql` endpoint.

### Authentication

Most API requests require a valid JWT token in the Authorization header:

```
Authorization: Bearer <your-jwt-token>
```

#### Getting a JWT Token

To get a JWT token, use the login endpoint:

```bash
curl -X POST http://localhost:8080/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "username": "admin",
    "password": "admin123"
  }'
```

Response:
```json
{
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "expires_in": 86400,
  "user": {
    "id": "admin-user-id",
    "username": "admin",
    "email": "<EMAIL>",
    "roles": ["admin"]
  }
}
```

#### Available Demo Users

- **Admin**: username: `admin`, password: `admin123`
- **User**: username: `user`, password: `password`
- **Guest**: username: `guest`, password: `password`

#### User Roles and Permissions

- **Admin**: Full access to all operations
- **User**: Can read and write files (own files only)
- **Guest**: Can only read files

### Base URL

- **Development**: `http://localhost:8080/graphql`
- **Production**: `https://your-domain.com/graphql`

## Types

### FileType

```graphql
enum FileType {
  IMAGE
  VIDEO
}
```

### FileStatus

```graphql
enum FileStatus {
  UPLOADING
  PROCESSING
  READY
  ERROR
}
```

### URLType

```graphql
enum URLType {
  PUBLIC
  SIGNED
}
```

### FileMetadata

```graphql
type FileMetadata {
  id: ID!
  filename: String!
  originalName: String!
  fileType: FileType!
  mimeType: String!
  size: Int!
  status: FileStatus!
  uploadedAt: String!
  updatedAt: String!
  publicURL: String
  cdnURL: String
  tags: [String!]
  metadata: String
}
```

## Queries

### file

Get a single file by ID.

```graphql
query GetFile($id: ID!) {
  file(id: $id) {
    id
    filename
    originalName
    fileType
    mimeType
    size
    status
    uploadedAt
    updatedAt
    publicURL
    cdnURL
    tags
    metadata
  }
}
```

**Variables:**
```json
{
  "id": "file-uuid-here"
}
```

### files

List files with optional filtering and pagination.

```graphql
query ListFiles($filter: FileFilter, $pagination: PaginationInput) {
  files(filter: $filter, pagination: $pagination) {
    files {
      id
      filename
      originalName
      fileType
      size
      status
      uploadedAt
      publicURL
      cdnURL
      tags
    }
    totalCount
    hasNextPage
    hasPreviousPage
  }
}
```

**Variables:**
```json
{
  "filter": {
    "fileType": "IMAGE",
    "status": "READY",
    "tags": ["profile"],
    "uploadedAfter": "2024-01-01T00:00:00Z",
    "uploadedBefore": "2024-12-31T23:59:59Z"
  },
  "pagination": {
    "limit": 20,
    "offset": 0
  }
}
```

### downloadURL

Generate a download URL for a file.

```graphql
query GenerateDownloadURL($id: ID!, $urlType: URLType, $expiresIn: Int) {
  downloadURL(id: $id, urlType: $urlType, expiresIn: $expiresIn) {
    success
    message
    url
    expiresAt
  }
}
```

**Variables:**
```json
{
  "id": "file-uuid-here",
  "urlType": "SIGNED",
  "expiresIn": 3600
}
```

### health

Health check endpoint.

```graphql
query {
  health
}
```

## Mutations

### uploadFile

Upload a file or generate a signed upload URL.

```graphql
mutation UploadFile($input: UploadInput!) {
  uploadFile(input: $input) {
    success
    message
    uploadURL
    file {
      id
      filename
      status
      publicURL
      cdnURL
    }
  }
}
```

**Variables for Signed Upload:**
```json
{
  "input": {
    "filename": "profile-image.jpg",
    "fileType": "IMAGE",
    "mimeType": "image/jpeg",
    "size": 1024000,
    "tags": ["profile", "avatar"],
    "metadata": "{\"userId\": \"123\", \"category\": \"profile\"}",
    "useSignedUpload": true
  }
}
```

**Variables for Direct Upload:**
```json
{
  "input": {
    "filename": "video.mp4",
    "fileType": "VIDEO",
    "mimeType": "video/mp4",
    "size": 50000000,
    "tags": ["content", "video"],
    "useSignedUpload": false
  }
}
```

### completeUpload

Mark a signed upload as complete.

```graphql
mutation CompleteUpload($id: ID!) {
  completeUpload(id: $id) {
    id
    filename
    status
    publicURL
    cdnURL
  }
}
```

**Variables:**
```json
{
  "id": "file-uuid-here"
}
```

### deleteFile

Delete a file and its metadata.

```graphql
mutation DeleteFile($id: ID!) {
  deleteFile(id: $id)
}
```

**Variables:**
```json
{
  "id": "file-uuid-here"
}
```

### updateFileMetadata

Update file tags and metadata.

```graphql
mutation UpdateFileMetadata($id: ID!, $tags: [String!], $metadata: String) {
  updateFileMetadata(id: $id, tags: $tags, metadata: $metadata) {
    id
    tags
    metadata
    updatedAt
  }
}
```

**Variables:**
```json
{
  "id": "file-uuid-here",
  "tags": ["updated", "profile", "avatar"],
  "metadata": "{\"userId\": \"123\", \"category\": \"profile\", \"updated\": true}"
}
```

## Error Handling

The API returns structured error responses:

```json
{
  "errors": [
    {
      "message": "File not found",
      "locations": [{"line": 2, "column": 3}],
      "path": ["file"],
      "extensions": {
        "code": "NOT_FOUND",
        "timestamp": "2024-01-01T12:00:00Z"
      }
    }
  ],
  "data": null
}
```

### Common Error Codes

- `NOT_FOUND`: Resource not found
- `VALIDATION_ERROR`: Input validation failed
- `UNAUTHORIZED`: Authentication required
- `FORBIDDEN`: Insufficient permissions
- `INTERNAL_ERROR`: Server error
- `RATE_LIMITED`: Too many requests

## Rate Limiting

The API implements rate limiting:

- **General API**: 10 requests per second
- **Upload endpoints**: 5 requests per second
- **Burst allowance**: 20 requests

Rate limit headers are included in responses:

```
X-RateLimit-Limit: 10
X-RateLimit-Remaining: 9
X-RateLimit-Reset: 1640995200
```

## File Upload Workflow

### Option 1: Signed Upload (Recommended for large files)

1. Call `uploadFile` mutation with `useSignedUpload: true`
2. Use the returned `uploadURL` to upload file directly to R2
3. Call `completeUpload` mutation to mark upload as complete

### Option 2: Direct Upload (For small files)

1. Call `uploadFile` mutation with `useSignedUpload: false`
2. Upload file content via REST API to `/upload` endpoint
3. File is processed automatically

## Best Practices

1. **Use signed uploads** for files larger than 10MB
2. **Add meaningful tags** for better organization
3. **Include metadata** for additional context
4. **Check file status** before generating download URLs
5. **Use CDN URLs** for better performance
6. **Implement retry logic** for failed uploads
7. **Validate file types** on client side
8. **Compress images** before upload when possible

## SDK Examples

### JavaScript/TypeScript

```typescript
import { GraphQLClient } from 'graphql-request';

const client = new GraphQLClient('http://localhost:8080/graphql', {
  headers: {
    Authorization: `Bearer ${token}`,
  },
});

// Upload file
const uploadMutation = `
  mutation UploadFile($input: UploadInput!) {
    uploadFile(input: $input) {
      success
      uploadURL
      file { id filename }
    }
  }
`;

const result = await client.request(uploadMutation, {
  input: {
    filename: 'image.jpg',
    fileType: 'IMAGE',
    mimeType: 'image/jpeg',
    size: 1024000,
    useSignedUpload: true,
  },
});
```

### cURL

```bash
# Upload file
curl -X POST http://localhost:8080/graphql \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-token" \
  -d '{
    "query": "mutation($input: UploadInput!) { uploadFile(input: $input) { success uploadURL file { id } } }",
    "variables": {
      "input": {
        "filename": "test.jpg",
        "fileType": "IMAGE",
        "mimeType": "image/jpeg",
        "size": 1024000,
        "useSignedUpload": true
      }
    }
  }'
```

## REST Endpoints

| Endpoint | Method | Description | Auth Required |
|----------|--------|-------------|---------------|
| `/health` | GET | Health check | No |
| `/graphql` | POST | GraphQL API | Yes (for most operations) |
| `/playground` | GET | GraphQL Playground | No |
| `/upload` | POST | Direct file upload | Yes |
| `/auth/login` | POST | User login | No |
| `/auth/refresh` | POST | Refresh JWT token | Yes |
| `/auth/me` | GET | Get current user info | Yes |

### Authentication Endpoints

#### POST /auth/login

Login with username and password to get JWT token.

**Request:**
```json
{
  "username": "admin",
  "password": "admin123"
}
```

**Response:**
```json
{
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "expires_in": 86400,
  "user": {
    "id": "admin-user-id",
    "username": "admin",
    "email": "<EMAIL>",
    "roles": ["admin"]
  }
}
```

#### POST /auth/refresh

Refresh an existing JWT token.

**Headers:**
```
Authorization: Bearer <current-token>
```

**Response:**
```json
{
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "expires_in": 86400
}
```

#### GET /auth/me

Get current user information.

**Headers:**
```
Authorization: Bearer <token>
```

**Response:**
```json
{
  "user_id": "admin-user-id",
  "username": "admin",
  "email": "<EMAIL>",
  "roles": ["admin"]
}
```
