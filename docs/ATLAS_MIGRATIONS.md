# Atlas Database Migrations

This document explains how to use Atlas for database schema management in the XBIT CDN Service.

## Overview

Atlas is a modern database schema management tool that provides:
- Schema-as-code approach using Go models
- Automatic migration generation by comparing desired vs current schema
- Safe migration planning and execution

**Note**: Atlas is configured for **local development only**. Other environments (unstable, staging, production) are handled by the DevOps team.

## Setup

### 1. Install Atlas (if not already installed)
```bash
go install ariga.io/atlas/cmd/atlas@latest
```

### 2. Setup Atlas Development Database
```bash
make atlas-setup
```

This creates a temporary database that Atlas uses for schema comparison.

## Main Commands

### Generate Migration Files
```bash
make db-diff
```

This command:
1. Compares your Go models in `internal/model/schema.go` with the current database schema
2. Generates migration files in the `migrations/` directory
3. Names files with timestamps and descriptive names

### Apply Migrations
```bash
make db-apply
```

This command:
1. Applies all pending migration files to the local database
2. Updates the migration history table
3. Ensures database schema matches your Go models

## Environment Configuration

### Local Environment (Development)
- Database URL: `postgres://postgres:password@localhost:5432/xbit_cdn_local?sslmode=disable`
- Dev Database: `postgres://postgres:password@localhost:5433/atlas_dev_local?sslmode=disable`
- Configuration: Uses default values in `atlas.hcl`

### Other Environments
- **Unstable, Staging & Production**: Migrations are handled by DevOps CI/CD pipeline
- Contact DevOps team for schema changes in these environments

## Workflow

### 1. Modify Database Schema
Edit the Go models in `internal/model/schema.go` to reflect your desired database schema.

### 2. Generate Migration
```bash
make db-diff
```

### 3. Review Generated Migration
Check the generated migration file in `migrations/` directory to ensure it's correct.

### 4. Apply Migration
```bash
make db-apply
```

### 5. Test Your Changes
Run your application and tests to ensure the schema changes work correctly.

### 6. Commit Changes
Commit both the model changes and the generated migration files.

## File Structure

```
├── atlas.hcl                    # Atlas configuration
├── internal/model/schema.go     # Go models defining desired schema
├── migrations/                  # Generated migration files
│   ├── 001_create_files_table.sql
│   └── ...
└── scripts/setup-atlas-dev-db.sh # Setup script for dev databases
```

## Best Practices

1. **Always review generated migrations** before applying them
2. **Test migrations on local environment** before deploying
3. **Keep Go models in sync** with actual database requirements
4. **Use descriptive names** when prompted for migration names
5. **Backup production data** before applying migrations (handled by DevOps)

## Troubleshooting

### Atlas Command Not Found
```bash
go install ariga.io/atlas/cmd/atlas@latest
```

### Dev Database Connection Issues
```bash
make atlas-setup
```

### Environment Variable Issues
Ensure your `.env.local` or `.env.unstable` files contain:
```
DB_HOST=localhost
DB_PORT=5432
DB_USER=postgres
DB_PASSWORD=password
DB_NAME=xbit_cdn_local
DB_SSLMODE=disable
```

### Migration Conflicts
If you have conflicts, you may need to:
1. Reset your local database: `make db-reset`
2. Regenerate migrations: `make db-diff`
3. Apply fresh migrations: `make db-apply`

## Legacy Commands

For backward compatibility, these commands are still available:
- `make db-migrate-local` → `make db-apply`
- `make db-migrate-unstable` → Handled by DevOps
- `make db-migrate-staging` → Handled by DevOps
- `make db-migrate-production` → Handled by DevOps
