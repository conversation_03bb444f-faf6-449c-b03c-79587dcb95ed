# Cloudflare R2 Setup Guide for XBIT CDN Service

## Overview

This guide helps DevOps team set up Cloudflare R2 storage for the XBIT CDN Service in different environments.

## Prerequisites

- Cloudflare account with R2 enabled
- Access to Cloudflare Dashboard
- Admin permissions to create buckets and API tokens

## Environment Configuration

### Unstable Environment

**Current Configuration:**
- Account ID: `23730d7e6dec9dfe0266e9df554e20c5`
- Access Key ID: `659727ae7f867e3a8430d26d8200b6dc`
- Secret Access Key: `3a91eb858bd3335ba0c7855fec0322794171a38d1aaec5794f931b0c489faa50`
- Bucket Name: `xbit-cdn-unstable`
- Endpoint: `https://23730d7e6dec9dfe0266e9df554e20c5.r2.cloudflarestorage.com`

## Step-by-Step Setup

### 1. Create R2 Bucket

1. **Login to Cloudflare Dashboard**
   - Go to [Cloudflare Dashboard](https://dash.cloudflare.com)
   - Navigate to R2 Object Storage

2. **Create Bucket**
   - Click "Create bucket"
   - Bucket name: `xbit-cdn-unstable`
   - Location: Choose appropriate location (Auto is recommended)
   - Click "Create bucket"

### 2. Configure Bucket Settings

#### 2.1 Public Access (if needed)
```bash
# If you need public access to files, configure custom domain
# This is optional - the application can work with signed URLs
```

#### 2.2 CORS Configuration
Set up CORS for web uploads:

```json
[
  {
    "AllowedOrigins": [
      "https://unstable.xbit.com",
      "https://admin-unstable.xbit.com",
      "http://localhost:3000",
      "http://localhost:8080"
    ],
    "AllowedMethods": ["GET", "PUT", "POST", "DELETE", "HEAD"],
    "AllowedHeaders": ["*"],
    "ExposeHeaders": ["ETag"],
    "MaxAgeSeconds": 3600
  }
]
```

### 3. Verify API Token Permissions

The provided API token should have the following permissions:

**Required Permissions:**
- `Object Storage:Edit` - For uploading, downloading, and deleting files
- `Object Storage:Read` - For listing and reading file metadata

**Optional Permissions:**
- `Zone:Read` - If using CDN features
- `Zone:Edit` - If setting up caching rules automatically

### 4. Test Configuration

After creating the bucket, test the configuration:

```bash
# From the project root directory
make test-r2-unstable
```

Expected output should show successful tests for:
- ✅ Bucket accessibility
- ✅ Object listing
- ✅ File upload
- ✅ File download
- ✅ File deletion

### 5. Troubleshooting

#### Common Issues

**403 Forbidden Error:**
```
❌ Failed to access bucket: operation error S3: HeadBucket, 
https response error StatusCode: 403, RequestID: , HostID: , 
api error Forbidden: Forbidden
```

**Solutions:**
1. Verify bucket name is correct: `xbit-cdn-unstable`
2. Check API token has `Object Storage:Edit` permissions
3. Ensure bucket exists in the correct Cloudflare account
4. Verify Account ID matches the endpoint URL

**Access Denied Error:**
```
❌ Failed to upload test file: operation error S3: PutObject, 
https response error StatusCode: 403, RequestID: , HostID: , 
api error AccessDenied: Access Denied
```

**Solutions:**
1. Check API token permissions include write access
2. Verify the token hasn't expired
3. Ensure bucket policy allows the operations

#### Manual Verification

You can manually verify the setup using AWS CLI with R2:

```bash
# Configure AWS CLI for R2
aws configure set aws_access_key_id 659727ae7f867e3a8430d26d8200b6dc
aws configure set aws_secret_access_key 3a91eb858bd3335ba0c7855fec0322794171a38d1aaec5794f931b0c489faa50
aws configure set region auto

# Test bucket access
aws s3 ls s3://xbit-cdn-unstable --endpoint-url=https://23730d7e6dec9dfe0266e9df554e20c5.r2.cloudflarestorage.com

# Test file upload
echo "test" | aws s3 cp - s3://xbit-cdn-unstable/test.txt --endpoint-url=https://23730d7e6dec9dfe0266e9df554e20c5.r2.cloudflarestorage.com

# Test file download
aws s3 cp s3://xbit-cdn-unstable/test.txt - --endpoint-url=https://23730d7e6dec9dfe0266e9df554e20c5.r2.cloudflarestorage.com

# Clean up
aws s3 rm s3://xbit-cdn-unstable/test.txt --endpoint-url=https://23730d7e6dec9dfe0266e9df554e20c5.r2.cloudflarestorage.com
```

## Environment-Specific Configurations

### Staging Environment
- Bucket Name: `xbit-cdn-staging`
- Follow same steps but use staging-specific names and URLs

### Production Environment
- Bucket Name: `xbit-cdn-production`
- Additional security considerations:
  - Enable versioning
  - Set up lifecycle policies
  - Configure backup strategies
  - Implement monitoring and alerting

## CDN Configuration (Optional)

If using Cloudflare CDN with custom domain:

1. **Set up Custom Domain**
   - Go to R2 bucket settings
   - Add custom domain (e.g., `unstable-cdn.xbit.com`)
   - Update DNS records as instructed

2. **Configure Caching Rules**
   - Images: Cache for 1 year
   - Videos: Cache for 1 month
   - API responses: No cache

3. **Update Environment Variables**
   ```bash
   CDN_BASE_URL=https://unstable-cdn.xbit.com
   CDN_ZONE_ID=your_zone_id
   CDN_API_TOKEN=your_cdn_api_token
   ```

## Security Best Practices

1. **API Token Security**
   - Use separate tokens for each environment
   - Rotate tokens regularly
   - Limit token permissions to minimum required

2. **Bucket Security**
   - Don't enable public access unless necessary
   - Use signed URLs for sensitive content
   - Implement proper CORS policies

3. **Monitoring**
   - Set up alerts for unusual access patterns
   - Monitor storage usage and costs
   - Log all API operations

## Support

If you encounter issues:

1. Check the application logs for detailed error messages
2. Verify all environment variables are set correctly
3. Test the configuration using the provided scripts
4. Contact the development team with specific error messages

## Next Steps

After successful R2 setup:

1. Deploy the application to unstable environment
2. Test file upload/download functionality
3. Monitor performance and costs
4. Set up similar configuration for staging/production environments
