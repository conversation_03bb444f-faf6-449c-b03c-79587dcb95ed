# XBIT CDN Service

A GraphQL-based CDN service built with Go, providing upload and download functionality for images and videos using Cloudflare R2 and CDN.

## Features

- 🚀 **GraphQL API** for file operations
- ☁️ **Cloudflare R2** storage integration
- 🌐 **Cloudflare CDN** for optimal performance
- 🔐 **Dual URL support**: Public URLs and Signed URLs
- 🔒 **JWT Authentication** and authorization
- 📁 **Multi-format support**: Images (JPG, PNG, GIF, WebP) and Videos (MP4, MOV, AVI, WebM)
- 📊 **File metadata management** with tags and custom metadata
- 🔍 **Advanced filtering** and pagination
- 🐳 **Docker support** with docker-compose
- 📈 **Production-ready** with Nginx reverse proxy

## Tech Stack

- **Language**: Go 1.21+
- **API**: GraphQL with custom resolvers
- **Storage**: Cloudflare R2 (S3-compatible)
- **CDN**: Cloudflare CDN
- **Database**: PostgreSQL
- **Cache**: Redis (optional)
- **Authentication**: JWT
- **Containerization**: Docker & Docker Compose
- **Reverse Proxy**: Nginx

## Project Structure

```
├── cmd/
│   └── server/          # Application entrypoint
├── internal/
│   ├── config/          # Configuration management
│   ├── handler/         # GraphQL handlers/resolvers
│   ├── service/         # Business logic layer
│   ├── repository/      # Data access layer
│   └── model/           # Data models
├── graph/               # GraphQL schema and generated code
├── migrations/          # Database migrations
├── pkg/                 # Shared packages
├── docs/               # Documentation
├── Dockerfile          # Docker configuration
├── docker-compose.yml  # Multi-service setup
├── nginx.conf          # Nginx configuration
└── Makefile           # Build and deployment scripts
```

## Quick Start

### Option 1: Docker Compose (Recommended)

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd xbit-cdn-service
   ```

2. **Configure environment variables**
   ```bash
   cp .env.example .env
   # Edit .env with your Cloudflare R2 credentials
   ```

3. **Start all services**
   ```bash
   make docker-compose-up
   ```

4. **Run database migrations**
   ```bash
   make db-migrate
   ```

5. **Access the service**
   - GraphQL Playground: http://localhost/playground
   - GraphQL API: http://localhost/graphql
   - Health Check: http://localhost/health

### Option 2: Local Development

1. **Prerequisites**
   - Go 1.21+
   - PostgreSQL 15+
   - Redis (optional)

2. **Setup development environment**
   ```bash
   make setup
   ```

3. **Configure environment**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

4. **Start dependencies**
   ```bash
   make docker-compose-up postgres redis
   ```

5. **Run migrations**
   ```bash
   make db-migrate
   ```

6. **Start development server**
   ```bash
   make dev  # Hot reload
   # or
   make run  # Single run
   ```

## Configuration

### Environment Variables

| Variable | Description | Default | Required |
|----------|-------------|---------|----------|
| `PORT` | Server port | `8080` | No |
| `ENV` | Environment | `development` | No |
| `DB_HOST` | Database host | `localhost` | Yes |
| `DB_PORT` | Database port | `5432` | No |
| `DB_USER` | Database user | `postgres` | Yes |
| `DB_PASSWORD` | Database password | - | Yes |
| `DB_NAME` | Database name | `xbit_cdn` | Yes |
| `R2_ACCOUNT_ID` | Cloudflare R2 Account ID | - | Yes |
| `R2_ACCESS_KEY_ID` | R2 Access Key | - | Yes |
| `R2_SECRET_ACCESS_KEY` | R2 Secret Key | - | Yes |
| `R2_BUCKET_NAME` | R2 Bucket Name | - | Yes |
| `R2_ENDPOINT` | R2 Endpoint URL | - | Yes |
| `CDN_BASE_URL` | CDN Base URL | - | No |
| `JWT_SECRET` | JWT Secret Key | `default-secret` | Yes |
| `MAX_FILE_SIZE` | Max upload size | `100MB` | No |

### Cloudflare R2 Setup

1. **Create R2 Bucket**
   - Go to Cloudflare Dashboard → R2 Object Storage
   - Create a new bucket
   - Note the bucket name and endpoint

2. **Generate API Tokens**
   - Go to R2 → Manage R2 API tokens
   - Create token with R2:Edit permissions
   - Note the Access Key ID and Secret Access Key

3. **Configure CDN (Optional)**
   - Set up custom domain for your R2 bucket
   - Configure caching rules
   - Update `CDN_BASE_URL` in your environment

## API Documentation

### GraphQL Schema

The service provides a GraphQL API with the following main operations:

#### Queries

```graphql
type Query {
  # Get file by ID
  file(id: ID!): FileMetadata

  # List files with filtering and pagination
  files(filter: FileFilter, pagination: PaginationInput): FileListResponse!

  # Generate download URL
  downloadURL(id: ID!, urlType: URLType = PUBLIC, expiresIn: Int): DownloadResponse!

  # Health check
  health: String!
}
```

#### Mutations

```graphql
type Mutation {
  # Upload file (returns signed URL or direct upload)
  uploadFile(input: UploadInput!): UploadResponse!

  # Complete signed upload
  completeUpload(id: ID!): FileMetadata

  # Delete file
  deleteFile(id: ID!): Boolean!

  # Update file metadata
  updateFileMetadata(id: ID!, tags: [String!], metadata: String): FileMetadata
}
```

### Example Usage

#### 1. Upload File (Signed URL)

```graphql
mutation {
  uploadFile(input: {
    filename: "image.jpg"
    fileType: IMAGE
    mimeType: "image/jpeg"
    size: 1024000
    tags: ["profile", "user"]
    useSignedUpload: true
  }) {
    success
    message
    uploadURL
    file {
      id
      filename
      status
    }
  }
}
```

#### 2. List Files

```graphql
query {
  files(
    filter: {
      fileType: IMAGE
      status: READY
      tags: ["profile"]
    }
    pagination: {
      limit: 10
      offset: 0
    }
  ) {
    files {
      id
      filename
      originalName
      fileType
      size
      uploadedAt
      publicURL
      cdnURL
    }
    totalCount
    hasNextPage
  }
}
```

#### 3. Generate Download URL

```graphql
query {
  downloadURL(
    id: "file-id-here"
    urlType: SIGNED
    expiresIn: 3600
  ) {
    success
    url
    expiresAt
  }
}
```

### REST Endpoints

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/health` | GET | Health check |
| `/graphql` | POST | GraphQL API |
| `/playground` | GET | GraphQL Playground |
| `/upload` | POST | Direct file upload |

## Development

### Available Commands

```bash
# Development
make dev          # Start with hot reload
make run          # Build and run
make build        # Build binary
make test         # Run tests
make test-coverage # Run tests with coverage

# Docker
make docker-build        # Build Docker image
make docker-compose-up   # Start all services
make docker-compose-down # Stop all services

# Database
make db-migrate   # Run migrations
make db-reset     # Reset database

# Code quality
make fmt          # Format code
make lint         # Run linter
```

### Project Guidelines

1. **Code Structure**: Follow clean architecture principles
2. **Error Handling**: Use structured error responses
3. **Logging**: Use structured logging with appropriate levels
4. **Testing**: Write unit tests for all business logic
5. **Documentation**: Keep API documentation up to date

## Deployment

### Production Deployment

1. **Build and push Docker image**
   ```bash
   make docker-build
   docker tag xbit-cdn-service:latest your-registry/xbit-cdn-service:latest
   docker push your-registry/xbit-cdn-service:latest
   ```

2. **Deploy with docker-compose**
   ```bash
   # On production server
   docker-compose -f docker-compose.prod.yml up -d
   ```

3. **Or deploy to Kubernetes**
   ```bash
   kubectl apply -f k8s/
   ```

### Environment-specific Configurations

- **Development**: Use docker-compose.yml
- **Staging**: Use docker-compose.staging.yml
- **Production**: Use docker-compose.prod.yml

## Monitoring and Observability

### Health Checks

- **Application**: `GET /health`
- **Database**: Built-in connection health check
- **R2 Storage**: Connection validation on startup

### Metrics

The service exposes metrics for:
- Request count and duration
- File upload/download statistics
- Error rates
- Database connection pool status

### Logging

Structured JSON logging with levels:
- `ERROR`: Application errors
- `WARN`: Warning conditions
- `INFO`: General information
- `DEBUG`: Detailed debug information

## Security

### Authentication

- JWT-based authentication
- Configurable token expiry
- Support for custom claims

### Authorization

- Role-based access control
- File ownership validation
- API rate limiting

### File Security

- File type validation
- Size limits
- Virus scanning (configurable)
- Signed URLs for secure access

## Performance

### Optimizations

- Connection pooling for database
- Redis caching for metadata
- CDN integration for global distribution
- Efficient file streaming

### Scaling

- Horizontal scaling support
- Load balancer ready
- Database read replicas support
- CDN edge caching

## Troubleshooting

### Common Issues

1. **Database Connection Failed**
   ```bash
   # Check database status
   make docker-compose-logs postgres

   # Reset database
   make db-reset
   ```

2. **R2 Upload Failed**
   - Verify R2 credentials in .env
   - Check bucket permissions
   - Validate endpoint URL

3. **File Not Found**
   - Check file status in database
   - Verify R2 bucket contents
   - Check CDN configuration

### Debug Mode

Enable debug logging:
```bash
export ENV=development
export LOG_LEVEL=debug
```

## Contributing

1. Fork the repository
2. Create feature branch: `git checkout -b feature/amazing-feature`
3. Commit changes: `git commit -m 'Add amazing feature'`
4. Push to branch: `git push origin feature/amazing-feature`
5. Open a Pull Request

### Development Setup

```bash
# Install development tools
make install-tools

# Setup pre-commit hooks
pre-commit install

# Run tests before committing
make test
```

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Support

For support and questions:
- Create an issue on GitHub
- Check the documentation in `/docs`
- Review the troubleshooting section

---

**Made with ❤️ for efficient file management and CDN distribution**