# Atlas configuration for XBIT CDN Service

# Local development environment only
# Other environments (unstable, staging, production) are handled by DevOps
env "local" {
  # URL of the database to inspect and apply migrations to
  url = "postgres://postgres:postgres@localhost:5433/xbit_cdn?sslmode=disable"

  # URL of the Dev Database for schema diffing
  dev = "postgres://postgres:postgres@localhost:5433/atlas_dev_local?sslmode=disable"

  # Path to the migration directory
  migration {
    dir = "file://migrations"
  }

  # Define the desired schema state using Go models
  schema {
    src = "file://internal/model/schema.go"
  }
}

# Default environment
variable "env" {
  type    = string
  default = "local"
}
